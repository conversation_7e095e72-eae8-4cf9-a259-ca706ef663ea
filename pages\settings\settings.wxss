/* pages/settings/settings.wxss - 量子设置版 */
.container {
  padding: calc(env(safe-area-inset-top) + 50rpx) clamp(40rpx, 8vw, 60rpx) calc(env(safe-area-inset-bottom) + 150rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  max-width: 750rpx;
  margin: 0 auto;
  animation: settings-materialize 1s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 设置物质化动画 */
@keyframes settings-materialize {
  0% {
    opacity: 0;
    transform: scale(0.95) rotateZ(-2deg);
    filter: blur(6rpx);
  }
  70% {
    opacity: 0.9;
    transform: scale(1.02) rotateZ(0deg);
    filter: blur(1rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotateZ(0deg);
    filter: blur(0);
  }
}

/* 页面标题 */
.page-title {
  font-size: clamp(48rpx, 10vw, 64rpx);
  font-weight: 200;
  margin-bottom: 60rpx;
  text-align: center;
  letter-spacing: 6rpx;
  background: linear-gradient(135deg, 
    #00D9FF 0%, 
    #8A2BE2 35%, 
    #FF0080 70%, 
    #00D9FF 100%);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: title-flow 4s ease-in-out infinite;
  position: relative;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -16rpx;
  left: 50%;
  width: 120rpx;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #00D9FF, transparent);
  transform: translateX(-50%);
  animation: title-line-glow 3s ease-in-out infinite;
}

@keyframes title-flow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes title-line-glow {
  0%, 100% {
    opacity: 0.5;
    width: 120rpx;
  }
  50% {
    opacity: 1;
    width: 200rpx;
  }
}

/* 设置项容器 */
.settings-section {
  width: 100%;
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(0, 217, 255, 0.1);
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  backdrop-filter: blur(30rpx);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 8rpx 40rpx rgba(0, 0, 0, 0.2),
    0 0 60rpx rgba(0, 217, 255, 0.05);
  transition: all 0.5s ease;
  animation: section-enter 0.6s ease-out backwards;
}

@keyframes section-enter {
  0% {
    opacity: 0;
    transform: translateY(40rpx) scale(0.95);
    filter: blur(4rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.settings-section::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
    rgba(0, 217, 255, 0.02) 0%,
    transparent 50%,
    rgba(138, 43, 226, 0.015) 100%);
  pointer-events: none;
}

.settings-section:hover {
  border-color: rgba(0, 217, 255, 0.2);
  box-shadow: 
    0 12rpx 50rpx rgba(0, 0, 0, 0.25),
    0 0 80rpx rgba(0, 217, 255, 0.1);
}

/* 设置项标题 */
.section-title {
  font-size: 36rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 30rpx;
  letter-spacing: 2rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 4rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #00D9FF, #8A2BE2);
  border-radius: 2rpx;
  transform: translateY(-50%);
}

/* 寿命设置 */
.lifespan-setting {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.current-lifespan {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1px solid rgba(0, 217, 255, 0.1);
}

.current-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 300;
  opacity: 0.9;
}

.current-value {
  font-size: 36rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #00D9FF, #8A2BE2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 2rpx;
}

/* 输入控制组 */
.input-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.input-label {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 400;
  letter-spacing: 1rpx;
  opacity: 0.9;
}

.input-container {
  position: relative;
}

.lifespan-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid rgba(0, 217, 255, 0.1);
  border-radius: 16rpx;
  padding: 28rpx 24rpx;
  color: var(--text-primary);
  font-size: 32rpx;
  font-weight: 400;
  text-align: center;
  transition: all 0.4s ease;
  backdrop-filter: blur(10rpx);
  box-sizing: border-box;
  letter-spacing: 2rpx;
}

.lifespan-input:focus {
  border-color: #00D9FF;
  background: rgba(0, 217, 255, 0.05);
  outline: none;
  box-shadow: 
    0 0 0 4rpx rgba(0, 217, 255, 0.1),
    0 0 40rpx rgba(0, 217, 255, 0.2);
  transform: scale(1.02);
}

.lifespan-input::placeholder {
  color: var(--text-tertiary);
  font-style: italic;
  opacity: 0.7;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.preset-btn {
  flex: 1;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid rgba(0, 217, 255, 0.15);
  border-radius: 16rpx;
  color: var(--text-secondary);
  font-size: 26rpx;
  font-weight: 400;
  transition: all 0.3s ease;
  backdrop-filter: blur(15rpx);
  cursor: pointer;
  letter-spacing: 1rpx;
}

.preset-btn:hover {
  border-color: rgba(0, 217, 255, 0.3);
  background: rgba(0, 217, 255, 0.05);
  color: var(--text-primary);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 217, 255, 0.1);
}

.preset-btn:active {
  transform: translateY(0);
  box-shadow: 0 4rpx 20rpx rgba(0, 217, 255, 0.1);
}

/* 保存按钮 */
.save-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, 
    #00D9FF 0%, 
    #8A2BE2 50%, 
    #FF0080 100%);
  border: none;
  border-radius: 20rpx;
  color: #FFFFFF;
  font-weight: 500;
  font-size: 32rpx;
  letter-spacing: 3rpx;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 
    0 8rpx 30rpx rgba(0, 217, 255, 0.3),
    0 0 60rpx rgba(0, 217, 255, 0.2);
  margin-top: 20rpx;
}

.save-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    transparent 50%, 
    rgba(255, 255, 255, 0.1) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.save-btn:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 
    0 15rpx 50rpx rgba(0, 217, 255, 0.4),
    0 0 80rpx rgba(138, 43, 226, 0.3);
}

.save-btn:hover::before {
  transform: translateX(100%);
}

.save-btn:active {
  transform: translateY(-2rpx) scale(1.01);
}

.save-btn:disabled {
  background: linear-gradient(135deg, #2A2A2A, #1A1A1A);
  color: #666666;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* 数据管理 */
.data-management {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.data-info {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.015);
  border: 1px solid rgba(0, 217, 255, 0.08);
  border-radius: 16rpx;
  backdrop-filter: blur(20rpx);
}

.data-info-title {
  font-size: 26rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.data-info-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  font-weight: 300;
  opacity: 0.8;
}

.data-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.data-btn {
  width: 100%;
  height: 88rpx;
  border: 2px solid;
  border-radius: 18rpx;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.4s ease;
  backdrop-filter: blur(20rpx);
  letter-spacing: 2rpx;
  position: relative;
  overflow: hidden;
}

.export-btn {
  background: rgba(0, 217, 255, 0.05);
  border-color: rgba(0, 217, 255, 0.2);
  color: #00D9FF;
}

.export-btn:hover {
  background: rgba(0, 217, 255, 0.1);
  border-color: rgba(0, 217, 255, 0.4);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 217, 255, 0.2);
}

.import-btn {
  background: rgba(138, 43, 226, 0.05);
  border-color: rgba(138, 43, 226, 0.2);
  color: #8A2BE2;
}

.import-btn:hover {
  background: rgba(138, 43, 226, 0.1);
  border-color: rgba(138, 43, 226, 0.4);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(138, 43, 226, 0.2);
}

.clear-btn {
  background: rgba(255, 0, 128, 0.05);
  border-color: rgba(255, 0, 128, 0.2);
  color: #FF0080;
}

.clear-btn:hover {
  background: rgba(255, 0, 128, 0.1);
  border-color: rgba(255, 0, 128, 0.4);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(255, 0, 128, 0.2);
}

.data-btn:active {
  transform: translateY(0);
}

/* 关于信息 */
.about-section {
  margin-top: 40rpx;
  text-align: center;
}

.app-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: center;
  padding: 40rpx 30rpx;
  background: rgba(255, 255, 255, 0.015);
  border: 1px solid rgba(0, 217, 255, 0.08);
  border-radius: 20rpx;
  backdrop-filter: blur(25rpx);
}

.app-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #00D9FF, #8A2BE2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 217, 255, 0.3);
}

.app-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 2rpx;
}

.app-version {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 300;
  opacity: 0.8;
}

.app-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  font-weight: 300;
  opacity: 0.9;
  text-align: center;
  margin-top: 20rpx;
  max-width: 500rpx;
}

/* 成功/错误提示 */
.toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 30rpx 40rpx;
  border-radius: 16rpx;
  backdrop-filter: blur(30rpx);
  font-size: 28rpx;
  font-weight: 500;
  letter-spacing: 1rpx;
  z-index: 1000;
  animation: toast-show 0.5s ease-out;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.toast.success {
  background: rgba(0, 255, 128, 0.9);
  color: #FFFFFF;
  border: 1px solid rgba(0, 255, 128, 0.3);
}

.toast.error {
  background: rgba(255, 0, 128, 0.9);
  color: #FFFFFF;
  border: 1px solid rgba(255, 0, 128, 0.3);
}

@keyframes toast-show {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 响应式优化 */
@media (max-width: 400px) {
  .container {
    padding-left: 30rpx;
    padding-right: 30rpx;
  }
  
  .page-title {
    font-size: 42rpx;
    letter-spacing: 4rpx;
    margin-bottom: 40rpx;
  }
  
  .settings-section {
    padding: 30rpx 20rpx;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .preset-btn {
    height: 70rpx;
  }
  
  .data-actions {
    gap: 16rpx;
  }
  
  .data-btn {
    height: 80rpx;
    font-size: 26rpx;
  }
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40rpx;
  height: 40rpx;
  margin: -20rpx 0 0 -20rpx;
  border: 4rpx solid rgba(0, 217, 255, 0.3);
  border-top: 4rpx solid #00D9FF;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 保存成功动画 */
.save-success {
  animation: save-success 0.8s ease-out;
}

@keyframes save-success {
  0% {
    transform: scale(1);
    box-shadow: 0 8rpx 30rpx rgba(0, 217, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 
      0 15rpx 50rpx rgba(0, 255, 128, 0.4),
      0 0 80rpx rgba(0, 255, 128, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8rpx 30rpx rgba(0, 217, 255, 0.3);
  }
} 