/* pages/settings/settings.wxss - 简洁版 */
.container {
  padding: calc(env(safe-area-inset-top) + 40rpx) clamp(32rpx, 6vw, 48rpx) calc(env(safe-area-inset-bottom) + 150rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  max-width: 750rpx;
  margin: 0 auto;
}

/* 页面标题 - 简洁版 */
.page-title {
  font-size: clamp(44rpx, 9vw, 56rpx);
  font-weight: 600;
  margin-bottom: var(--spacing-xl);
  text-align: center;
  letter-spacing: 1rpx;
  color: var(--text-primary);
}

/* 设置项容器 - 简洁版 */
.settings-section {
  width: 100%;
  margin-bottom: var(--spacing-lg);
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.settings-section:hover {
  background: var(--card-hover);
  box-shadow: var(--shadow-md);
}

/* 设置项标题 - 简洁版 */
.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

/* 寿命设置 - 简洁版 */
.lifespan-setting {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.current-lifespan {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) 0;
  border-bottom: 1px solid var(--card-border);
}

.current-label {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 400;
}

.current-value {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

/* 输入控制组 - 简洁版 */
.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.input-label {
  font-size: 26rpx;
  color: var(--text-primary);
  font-weight: 400;
}

.input-container {
  position: relative;
}

.lifespan-input {
  width: 100%;
  background: var(--surface-primary);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius-input);
  padding: var(--spacing-md);
  color: var(--text-primary);
  font-size: 28rpx;
  font-weight: 400;
  text-align: center;
  transition: all var(--transition-fast);
  box-sizing: border-box;
}

.lifespan-input:focus {
  border-color: var(--primary-400);
  background: var(--card-hover);
  outline: none;
  box-shadow: 0 0 0 2rpx rgba(0, 180, 219, 0.2);
}

.lifespan-input::placeholder {
  color: var(--text-tertiary);
}

/* 按钮组 - 简洁版 */
.button-group {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.preset-btn {
  flex: 1;
  height: 72rpx;
  background: var(--surface-primary);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius-btn);
  color: var(--text-secondary);
  font-size: 24rpx;
  font-weight: 400;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.preset-btn:hover {
  background: var(--card-hover);
  color: var(--text-primary);
  border-color: var(--primary-400);
}

.preset-btn:active {
  transform: scale(0.98);
}

/* 保存按钮 - 简洁版 */
.save-btn {
  width: 100%;
  height: 88rpx;
  background: var(--primary-500);
  border: none;
  border-radius: var(--border-radius-btn);
  color: var(--text-primary);
  font-weight: 500;
  font-size: 30rpx;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
  margin-top: var(--spacing-md);
}

.save-btn:hover {
  background: var(--primary-400);
  box-shadow: var(--shadow-md);
}

.save-btn:active {
  transform: scale(0.98);
}

.save-btn:disabled {
  background: var(--surface-secondary);
  color: var(--text-muted);
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.6;
}

/* 数据管理 */
.data-management {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.data-info {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.015);
  border: 1px solid rgba(0, 217, 255, 0.08);
  border-radius: 16rpx;
  backdrop-filter: blur(20rpx);
}

.data-info-title {
  font-size: 26rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.data-info-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  font-weight: 300;
  opacity: 0.8;
}

.data-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.data-btn {
  width: 100%;
  height: 88rpx;
  border: 2px solid;
  border-radius: 18rpx;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.4s ease;
  backdrop-filter: blur(20rpx);
  letter-spacing: 2rpx;
  position: relative;
  overflow: hidden;
}

.export-btn {
  background: rgba(0, 217, 255, 0.05);
  border-color: rgba(0, 217, 255, 0.2);
  color: #00D9FF;
}

.export-btn:hover {
  background: rgba(0, 217, 255, 0.1);
  border-color: rgba(0, 217, 255, 0.4);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 217, 255, 0.2);
}

.import-btn {
  background: rgba(138, 43, 226, 0.05);
  border-color: rgba(138, 43, 226, 0.2);
  color: #8A2BE2;
}

.import-btn:hover {
  background: rgba(138, 43, 226, 0.1);
  border-color: rgba(138, 43, 226, 0.4);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(138, 43, 226, 0.2);
}

.clear-btn {
  background: rgba(255, 0, 128, 0.05);
  border-color: rgba(255, 0, 128, 0.2);
  color: #FF0080;
}

.clear-btn:hover {
  background: rgba(255, 0, 128, 0.1);
  border-color: rgba(255, 0, 128, 0.4);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(255, 0, 128, 0.2);
}

.data-btn:active {
  transform: translateY(0);
}

/* 关于信息 */
.about-section {
  margin-top: 40rpx;
  text-align: center;
}

.app-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: center;
  padding: 40rpx 30rpx;
  background: rgba(255, 255, 255, 0.015);
  border: 1px solid rgba(0, 217, 255, 0.08);
  border-radius: 20rpx;
  backdrop-filter: blur(25rpx);
}

.app-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #00D9FF, #8A2BE2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 217, 255, 0.3);
}

.app-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 2rpx;
}

.app-version {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 300;
  opacity: 0.8;
}

.app-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  font-weight: 300;
  opacity: 0.9;
  text-align: center;
  margin-top: 20rpx;
  max-width: 500rpx;
}

/* 成功/错误提示 */
.toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 30rpx 40rpx;
  border-radius: 16rpx;
  backdrop-filter: blur(30rpx);
  font-size: 28rpx;
  font-weight: 500;
  letter-spacing: 1rpx;
  z-index: 1000;
  animation: toast-show 0.5s ease-out;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.toast.success {
  background: rgba(0, 255, 128, 0.9);
  color: #FFFFFF;
  border: 1px solid rgba(0, 255, 128, 0.3);
}

.toast.error {
  background: rgba(255, 0, 128, 0.9);
  color: #FFFFFF;
  border: 1px solid rgba(255, 0, 128, 0.3);
}

@keyframes toast-show {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 响应式优化 */
@media (max-width: 400px) {
  .container {
    padding-left: 30rpx;
    padding-right: 30rpx;
  }
  
  .page-title {
    font-size: 42rpx;
    letter-spacing: 4rpx;
    margin-bottom: 40rpx;
  }
  
  .settings-section {
    padding: 30rpx 20rpx;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .preset-btn {
    height: 70rpx;
  }
  
  .data-actions {
    gap: 16rpx;
  }
  
  .data-btn {
    height: 80rpx;
    font-size: 26rpx;
  }
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40rpx;
  height: 40rpx;
  margin: -20rpx 0 0 -20rpx;
  border: 4rpx solid rgba(0, 217, 255, 0.3);
  border-top: 4rpx solid #00D9FF;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 保存成功动画 */
.save-success {
  animation: save-success 0.8s ease-out;
}

@keyframes save-success {
  0% {
    transform: scale(1);
    box-shadow: 0 8rpx 30rpx rgba(0, 217, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 
      0 15rpx 50rpx rgba(0, 255, 128, 0.4),
      0 0 80rpx rgba(0, 255, 128, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8rpx 30rpx rgba(0, 217, 255, 0.3);
  }
} 