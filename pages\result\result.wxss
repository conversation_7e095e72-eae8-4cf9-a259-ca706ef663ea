/* pages/result/result.wxss - 简洁现代版 */
.container {
  padding: calc(env(safe-area-inset-top) + 40rpx) clamp(32rpx, 6vw, 48rpx) calc(env(safe-area-inset-bottom) + 150rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  max-width: 750rpx;
  margin: 0 auto;
}

/* 重要提醒 */
.important-reminder {
  width: 100%;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--accent-500) 100%);
  color: var(--text-primary);
  padding: var(--spacing-xl);
  text-align: center;
  font-size: clamp(28rpx, 6vw, 34rpx);
  font-weight: 600;
  border-radius: var(--border-radius-card);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-primary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* 时间显示区域 */
.current-time {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius-card);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
}

.current-date {
  font-size: clamp(30rpx, 6vw, 36rpx);
  font-weight: 500;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  letter-spacing: 1rpx;
}

.current-time-display {
  font-size: clamp(44rpx, 9vw, 56rpx);
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  font-weight: 600;
  color: var(--text-accent);
  letter-spacing: 2rpx;
  text-shadow: 0 0 20rpx rgba(100, 255, 218, 0.4);
}

/* 生命统计简化 */
.life-stats {
  width: 100%;
  margin-bottom: 50rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40rpx;
}

/* 剩余天数卡片 */
.remaining {
  width: 100%;
  text-align: center;
  padding: 50rpx 40rpx;
  background: rgba(255, 255, 255, 0.015);
  border: 2px solid;
  border-image: linear-gradient(45deg, 
    #00D9FF 0%, 
    #8A2BE2 50%, 
    #FF0080 100%) 1;
  border-radius: 28rpx;
  backdrop-filter: blur(30rpx);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 12rpx 48rpx rgba(0, 0, 0, 0.25),
    0 0 80rpx rgba(0, 217, 255, 0.15);
}

.remaining::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, 
    #00D9FF 0%, 
    #8A2BE2 50%, 
    #FF0080 100%);
  border-radius: 28rpx;
  z-index: -1;
  animation: remaining-glow 4s ease-in-out infinite;
  filter: blur(8rpx);
}

@keyframes remaining-glow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.98);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

.remaining .number {
  font-size: clamp(76rpx, 16vw, 100rpx);
  font-weight: 200;
  text-align: center;
  background: linear-gradient(135deg, 
    #00D9FF 0%, 
    #8A2BE2 35%, 
    #FF0080 70%, 
    #00D9FF 100%);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.1;
  margin-bottom: 20rpx;
  animation: number-pulse 3s ease-in-out infinite;
  letter-spacing: 2rpx;
}

@keyframes number-pulse {
  0%, 100% {
    background-position: 0% 50%;
    transform: scale(1);
  }
  50% {
    background-position: 100% 50%;
    transform: scale(1.05);
  }
}

.remaining .label {
  font-size: clamp(26rpx, 5vw, 32rpx);
  color: var(--text-secondary);
  font-weight: 300;
  letter-spacing: 2rpx;
  opacity: 0.9;
}

/* 进度环容器 */
.progress-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 50rpx;
  position: relative;
}

.progress-circle {
  position: relative;
  width: 320rpx;
  height: 320rpx;
}

.progress-circle canvas {
  transform: rotate(-90deg);
  filter: drop-shadow(0 0 20rpx rgba(0, 217, 255, 0.3));
}

/* 金句区域 */
.quote-section {
  width: 100%;
  margin-bottom: 50rpx;
  padding: 50rpx 40rpx;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(0, 217, 255, 0.1);
  border-radius: 24rpx;
  backdrop-filter: blur(25rpx);
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 8rpx 40rpx rgba(0, 0, 0, 0.15),
    inset 0 1px 2rpx rgba(255, 255, 255, 0.05);
}

.quote-section::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
    rgba(0, 217, 255, 0.02) 0%,
    transparent 50%,
    rgba(138, 43, 226, 0.015) 100%);
  pointer-events: none;
}

.quote {
  font-size: clamp(28rpx, 6vw, 36rpx);
  line-height: 1.8;
  color: var(--text-primary);
  font-weight: 300;
  font-style: italic;
  margin-bottom: 30rpx;
  letter-spacing: 1rpx;
  opacity: 0.95;
}

.quote-author {
  font-size: clamp(22rpx, 4vw, 26rpx);
  color: var(--text-secondary);
  font-weight: 400;
  opacity: 0.8;
  letter-spacing: 1rpx;
}

/* 操作按钮区域 */
.actions {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.action-btn {
  width: 100%;
  height: 96rpx;
  border: none;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 2rpx;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(20rpx);
}

.action-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    transparent 50%, 
    rgba(255, 255, 255, 0.1) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.action-btn:hover::before {
  transform: translateX(100%);
}

.share-btn {
  background: linear-gradient(135deg, 
    #00D9FF 0%, 
    #8A2BE2 50%, 
    #FF0080 100%);
  color: #FFFFFF;
  box-shadow: 
    0 8rpx 30rpx rgba(0, 217, 255, 0.25),
    0 0 60rpx rgba(0, 217, 255, 0.15);
}

.share-btn:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 
    0 15rpx 50rpx rgba(0, 217, 255, 0.35),
    0 0 80rpx rgba(138, 43, 226, 0.25);
}

.recalculate-btn {
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid rgba(0, 217, 255, 0.2);
  color: var(--text-primary);
  box-shadow: 
    0 8rpx 30rpx rgba(0, 0, 0, 0.1),
    0 0 40rpx rgba(0, 217, 255, 0.1);
}

.recalculate-btn:hover {
  border-color: rgba(0, 217, 255, 0.4);
  background: rgba(0, 217, 255, 0.05);
  transform: translateY(-2rpx) scale(1.01);
  box-shadow: 
    0 12rpx 40rpx rgba(0, 0, 0, 0.15),
    0 0 60rpx rgba(0, 217, 255, 0.2);
}

/* 响应式优化 */
@media (max-width: 400px) {
  .container {
    padding-left: 30rpx;
    padding-right: 30rpx;
  }
  
  .important-reminder,
  .current-time,
  .remaining,
  .quote-section {
    padding: 30rpx 20rpx;
  }
  
  .progress-circle {
    width: 280rpx;
    height: 280rpx;
  }
  
  .actions {
    gap: 20rpx;
  }
}

/* 加载动画 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40rpx;
  height: 40rpx;
  margin: -20rpx 0 0 -20rpx;
  border: 4rpx solid rgba(0, 217, 255, 0.3);
  border-top: 4rpx solid #00D9FF;
  border-radius: 50%;
  animation: loading-rotate 1s linear infinite;
}

@keyframes loading-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 