/* pages/result/result.wxss - 简洁现代版 */
.container {
  padding: calc(env(safe-area-inset-top) + 40rpx) clamp(32rpx, 6vw, 48rpx) calc(env(safe-area-inset-bottom) + 150rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  max-width: 750rpx;
  margin: 0 auto;
}

/* 重要提醒 - 简洁版 */
.important-reminder {
  width: 100%;
  background: var(--primary-500);
  color: var(--text-primary);
  padding: var(--spacing-lg);
  text-align: center;
  font-size: clamp(26rpx, 5vw, 30rpx);
  font-weight: 500;
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

/* 时间显示区域 - 简洁版 */
.current-time {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.current-date {
  font-size: clamp(28rpx, 5vw, 32rpx);
  font-weight: 400;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.current-time-display {
  font-size: clamp(40rpx, 8vw, 48rpx);
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  font-weight: 500;
  color: var(--text-primary);
  letter-spacing: 1rpx;
}

/* 生命统计 - 简洁版 */
.life-stats {
  width: 100%;
  margin-bottom: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

/* 剩余天数卡片 - 简洁版 */
.remaining {
  width: 100%;
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.remaining .number {
  font-size: clamp(64rpx, 14vw, 80rpx);
  font-weight: 600;
  text-align: center;
  color: var(--text-primary);
  line-height: 1.1;
  margin-bottom: var(--spacing-md);
}

.remaining .label {
  font-size: clamp(24rpx, 5vw, 28rpx);
  color: var(--text-secondary);
  font-weight: 400;
}

/* 进度环容器 - 简洁版 */
.progress-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.progress-circle {
  position: relative;
  width: 280rpx;
  height: 280rpx;
}

.progress-circle canvas {
  transform: rotate(-90deg);
}

/* 金句区域 - 简洁版 */
.quote-section {
  width: 100%;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius);
  text-align: center;
  box-shadow: var(--shadow-sm);
}

.quote {
  font-size: clamp(26rpx, 5vw, 30rpx);
  line-height: 1.6;
  color: var(--text-primary);
  font-weight: 400;
  font-style: italic;
  margin-bottom: var(--spacing-md);
}

.quote-author {
  font-size: clamp(20rpx, 4vw, 24rpx);
  color: var(--text-secondary);
  font-weight: 400;
}

/* 操作按钮区域 - 简洁版 */
.actions {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.action-btn {
  width: 100%;
  height: 88rpx;
  border: none;
  border-radius: var(--border-radius-btn);
  font-size: 30rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.share-btn {
  background: var(--primary-500);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
}

.share-btn:hover {
  background: var(--primary-400);
  box-shadow: var(--shadow-md);
}

.recalculate-btn {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
}

.recalculate-btn:hover {
  background: var(--card-hover);
  box-shadow: var(--shadow-md);
}

/* 响应式优化 */
@media (max-width: 400px) {
  .container {
    padding-left: 30rpx;
    padding-right: 30rpx;
  }
  
  .important-reminder,
  .current-time,
  .remaining,
  .quote-section {
    padding: 30rpx 20rpx;
  }
  
  .progress-circle {
    width: 280rpx;
    height: 280rpx;
  }
  
  .actions {
    gap: 20rpx;
  }
}

/* 加载动画 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40rpx;
  height: 40rpx;
  margin: -20rpx 0 0 -20rpx;
  border: 4rpx solid rgba(0, 217, 255, 0.3);
  border-top: 4rpx solid #00D9FF;
  border-radius: 50%;
  animation: loading-rotate 1s linear infinite;
}

@keyframes loading-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 