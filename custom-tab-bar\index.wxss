.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: calc(120rpx + env(safe-area-inset-bottom));
  background: rgba(15, 32, 39, 0.95);
  display: flex;
  z-index: 1000;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 1px solid rgba(100, 255, 218, 0.2);
}

.tab-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, var(--accent-500) 50%, transparent 100%);
  opacity: 0.6;
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-muted);
  font-size: 28rpx;
  font-weight: 500;
  transition: all var(--transition-smooth);
  position: relative;
  height: 120rpx;
  cursor: pointer;
  border-radius: var(--border-radius);
  margin: var(--spacing-sm);
}

.tab-bar-item::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--surface-primary);
  border-radius: var(--border-radius);
  opacity: 0;
  transition: all var(--transition-smooth);
  z-index: -1;
}

.tab-bar-item:hover::before {
  opacity: 1;
}

.tab-bar-item-active {
  color: var(--text-accent);
  font-size: 32rpx;
  font-weight: 600;
  text-shadow: 0 0 20rpx rgba(100, 255, 218, 0.6);
  background: var(--card-bg);
  border: 1px solid var(--accent-500);
  box-shadow: var(--shadow-glow);
}

.tab-bar-item-active::before {
  opacity: 1;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(0, 212, 255, 0.05) 100%);
}

.tab-bar-item-text {
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  text-shadow: inherit;
  line-height: 1.2;
  letter-spacing: 1rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-smooth);
  word-break: keep-all;
  white-space: nowrap;
}

/* 点击效果 */
.tab-bar-item:active {
  transform: scale(0.95);
  background: var(--surface-primary);
}

.tab-bar-item-active:active {
  transform: scale(0.98);
}

/* 设备适配 */
@media screen and (max-width: 320px) {
  .tab-bar {
    height: calc(100rpx + env(safe-area-inset-bottom));
  }

  .tab-bar-item {
    font-size: 24rpx;
    height: 100rpx;
  }

  .tab-bar-item-active {
    font-size: 26rpx;
  }
}

@media screen and (min-width: 414px) {
  .tab-bar {
    height: calc(140rpx + env(safe-area-inset-bottom));
  }

  .tab-bar-item {
    font-size: 30rpx;
    height: 140rpx;
  }

  .tab-bar-item-active {
    font-size: 34rpx;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .tab-item:hover {
    background: none;
    transform: none;
  }
  
  .tab-item:hover .tab-icon {
    filter: brightness(0.7);
    transform: none;
  }
  
  .tab-item:hover .tab-text {
    color: var(--text-tertiary);
    transform: none;
  }
  
  .tab-item:active {
    background: var(--glass-active);
    transform: scale(0.98);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .tab-item,
  .tab-icon,
  .tab-text,
  .tab-indicator,
  .tab-icon-container,
  .custom-tab-bar,
  .custom-tab-bar::before {
    animation: none !important;
    transition: none !important;
  }
  
  .tab-item.active .tab-icon-container {
    animation: none;
  }
  
  .tab-item.active .tab-text {
    animation: none;
  }
  
  .tab-item.active .tab-indicator {
    animation: none;
  }
}