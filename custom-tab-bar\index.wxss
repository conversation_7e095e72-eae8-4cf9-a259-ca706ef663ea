.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: calc(100rpx + env(safe-area-inset-bottom));
  background: rgba(15, 32, 39, 0.95);
  display: flex;
  z-index: 1000;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 1px solid var(--card-border);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-muted);
  font-size: 26rpx;
  font-weight: 400;
  transition: all var(--transition-fast);
  position: relative;
  height: 100rpx;
  cursor: pointer;
  border-radius: var(--border-radius);
  margin: var(--spacing-xs);
}

.tab-bar-item-active {
  color: var(--text-primary);
  font-size: 28rpx;
  font-weight: 500;
  background: var(--surface-primary);
  border: 1px solid var(--card-border);
}

.tab-bar-item-text {
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  line-height: 1.2;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  word-break: keep-all;
  white-space: nowrap;
}

/* 点击效果 */
.tab-bar-item:active {
  transform: scale(0.95);
}

.tab-bar-item-active:active {
  transform: scale(0.98);
}

/* 设备适配 */
@media screen and (max-width: 320px) {
  .tab-bar {
    height: calc(100rpx + env(safe-area-inset-bottom));
  }

  .tab-bar-item {
    font-size: 24rpx;
    height: 100rpx;
  }

  .tab-bar-item-active {
    font-size: 26rpx;
  }
}

@media screen and (min-width: 414px) {
  .tab-bar {
    height: calc(140rpx + env(safe-area-inset-bottom));
  }

  .tab-bar-item {
    font-size: 30rpx;
    height: 140rpx;
  }

  .tab-bar-item-active {
    font-size: 34rpx;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .tab-item:hover {
    background: none;
    transform: none;
  }
  
  .tab-item:hover .tab-icon {
    filter: brightness(0.7);
    transform: none;
  }
  
  .tab-item:hover .tab-text {
    color: var(--text-tertiary);
    transform: none;
  }
  
  .tab-item:active {
    background: var(--glass-active);
    transform: scale(0.98);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .tab-item,
  .tab-icon,
  .tab-text,
  .tab-indicator,
  .tab-icon-container,
  .custom-tab-bar,
  .custom-tab-bar::before {
    animation: none !important;
    transition: none !important;
  }
  
  .tab-item.active .tab-icon-container {
    animation: none;
  }
  
  .tab-item.active .tab-text {
    animation: none;
  }
  
  .tab-item.active .tab-indicator {
    animation: none;
  }
}