.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: calc(100rpx + env(safe-area-inset-bottom));
  background: rgba(30, 60, 114, 0.95);
  display: flex;
  z-index: 1000;
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-secondary);
  font-size: 28rpx;
  font-weight: 500;
  transition: all var(--transition-fast);
  position: relative;
  height: 100rpx;
  cursor: pointer;
}

.tab-bar-item::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(93, 156, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  z-index: -1;
}

.tab-bar-item-active {
  color: #ffffff;
  font-size: clamp(32rpx, 6.5vw, 40rpx);
  font-weight: 700;
  text-shadow: 0 0 15rpx rgba(93, 156, 255, 0.8);
  transform: scale(1.05);
  background: rgba(93, 156, 255, 0.1);
  border: 1px solid rgba(93, 156, 255, 0.3);
}

.tab-bar-item-active::before {
  width: clamp(100rpx, 20vw, 130rpx);
  height: clamp(100rpx, 20vw, 130rpx);
  background: radial-gradient(circle, rgba(93, 156, 255, 0.2) 0%, transparent 70%);
}

.tab-bar-item-text {
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  text-shadow: inherit;
  line-height: 1.2;
  letter-spacing: 1rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  word-break: keep-all;
  white-space: nowrap;
}

/* 点击时的动画效果优化 */
.tab-bar-item:active {
  transform: scale(0.95);
  background: rgba(93, 156, 255, 0.05);
}

.tab-bar-item-active:active {
  transform: scale(1.0);
}

/* 超小屏幕适配 */
@media screen and (max-width: 320px) {
  .tab-bar {
    bottom: calc(env(safe-area-inset-bottom) + 50rpx);
    height: 80rpx !important;
    margin: 0 8rpx;
    border-radius: 12rpx !important;
  }
  
  .tab-bar-item {
    font-size: 24rpx !important;
    min-height: 80rpx !important;
    padding: 6rpx !important;
  }
  
  .tab-bar-item-active {
    font-size: 28rpx !important;
    transform: scale(1.02) !important;
  }
  
  .tab-bar-item-active::before {
    width: 80rpx !important;
    height: 80rpx !important;
  }
  
  .tab-bar-border {
    border-radius: 12rpx 12rpx 0 0 !important;
  }
}

/* 小屏幕适配 */
@media screen and (max-width: 375px) {
  .tab-bar {
    bottom: calc(env(safe-area-inset-bottom) + 55rpx);
    height: clamp(85rpx, 17vw, 100rpx) !important;
    margin: 0 10rpx;
  }
  
  .tab-bar-item {
    font-size: clamp(26rpx, 5.5vw, 32rpx) !important;
  }
  
  .tab-bar-item-active {
    font-size: clamp(30rpx, 6vw, 36rpx) !important;
  }
}

/* 大屏幕适配 */
@media screen and (min-width: 414px) {
  .tab-bar {
    bottom: calc(env(safe-area-inset-bottom) + 70rpx);
    height: clamp(95rpx, 19vw, 120rpx) !important;
    margin: 0 15rpx;
    max-width: 650rpx;
  }
  
  .tab-bar-item {
    font-size: clamp(30rpx, 6vw, 38rpx) !important;
  }
  
  .tab-bar-item-active {
    font-size: clamp(34rpx, 7vw, 42rpx) !important;
  }
}

/* 超大屏幕适配 */
@media screen and (min-width: 500px) {
  .tab-bar {
    bottom: calc(env(safe-area-inset-bottom) + 80rpx);
    height: clamp(100rpx, 20vw, 130rpx) !important;
    margin: 0 20rpx;
    max-width: 700rpx;
  }
  
  .tab-bar-item {
    font-size: clamp(32rpx, 6.5vw, 40rpx) !important;
    padding: clamp(10rpx, 2vw, 15rpx) !important;
  }
  
  .tab-bar-item-active {
    font-size: clamp(36rpx, 7.5vw, 44rpx) !important;
  }
  
  .tab-bar-item-active::before {
    width: clamp(110rpx, 22vw, 140rpx) !important;
    height: clamp(110rpx, 22vw, 140rpx) !important;
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
  .tab-bar {
    bottom: calc(env(safe-area-inset-bottom) + 40rpx);
    height: clamp(70rpx, 14vw, 90rpx) !important;
    margin: 0 clamp(15rpx, 3vw, 30rpx);
    border-radius: clamp(12rpx, 2vw, 18rpx) !important;
  }
  
  .tab-bar-item {
    font-size: clamp(24rpx, 5vw, 32rpx) !important;
    min-height: clamp(70rpx, 14vw, 90rpx) !important;
    padding: clamp(6rpx, 1vw, 10rpx) !important;
  }
  
  .tab-bar-item-active {
    font-size: clamp(28rpx, 5.5vw, 36rpx) !important;
    transform: scale(1.03) !important;
  }
  
  .tab-bar-item-active::before {
    width: clamp(80rpx, 16vw, 110rpx) !important;
    height: clamp(80rpx, 16vw, 110rpx) !important;
  }
  
  .tab-bar-border {
    border-radius: clamp(12rpx, 2vw, 18rpx) clamp(12rpx, 2vw, 18rpx) 0 0 !important;
  }
}

/* 触摸优化 */
@media (hover: none) and (pointer: coarse) {
  .tab-bar-item {
    padding: clamp(10rpx, 2vw, 15rpx);
  }
  
  .tab-bar-item:active {
    background: rgba(93, 156, 255, 0.15);
    transform: scale(0.92);
  }
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .tab-bar-item {
    font-size: 30rpx;
  }
  
  .tab-bar-item-active {
    font-size: 34rpx;
  }
}

/* 适配刘海屏 */
.tab-bar {
  padding-bottom: env(safe-area-inset-bottom);
  height: calc(100rpx + env(safe-area-inset-bottom));
}

.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: calc(100rpx + env(safe-area-inset-bottom));
  background: rgba(21, 21, 31, 0.85);
  backdrop-filter: blur(var(--blur-strong));
  border-top: 1px solid var(--glass-border);
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  overflow: hidden;
}

.custom-tab-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--accent-500) 50%, transparent 100%);
  opacity: 0.6;
  z-index: 1;
}

/* TabBar内容区域 */
.tab-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 100rpx;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 2;
}

/* Tab项 */
.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  border-radius: var(--border-radius-input);
  margin: 0 var(--spacing-xs);
  overflow: hidden;
}

.tab-item:hover {
  background: var(--glass-hover);
  transform: translateY(-2rpx);
}

.tab-item:active {
  transform: scale(0.95) translateY(0);
}

/* 活跃状态背景 */
.tab-item.active {
  background: linear-gradient(135deg, rgba(23, 224, 255, 0.15) 0%, rgba(108, 93, 211, 0.08) 100%);
  border: 1px solid rgba(23, 224, 255, 0.2);
  box-shadow: 0 0 16rpx rgba(23, 224, 255, 0.2);
}

.tab-item.active::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(23, 224, 255, 0.1) 0%, transparent 70%);
  border-radius: var(--border-radius-input);
  pointer-events: none;
}

/* Tab图标容器 */
.tab-icon-container {
  position: relative;
  margin-bottom: var(--spacing-xs);
  transition: all 0.3s ease;
}

.tab-item.active .tab-icon-container {
  animation: tab-icon-float 2s ease-in-out infinite alternate;
}

@keyframes tab-icon-float {
  0% { transform: translateY(0) rotate(0deg); }
  100% { transform: translateY(-2rpx) rotate(2deg); }
}

/* Tab图标 */
.tab-icon {
  width: 44rpx;
  height: 44rpx;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  filter: brightness(0.7);
}

.tab-item.active .tab-icon {
  filter: brightness(1) drop-shadow(0 0 8rpx rgba(23, 224, 255, 0.6));
  transform: scale(1.1);
}

.tab-item:hover .tab-icon {
  filter: brightness(0.9);
  transform: scale(1.05);
}

/* Tab文字 */
.tab-text {
  font-size: 20rpx;
  font-weight: 500;
  color: var(--text-tertiary);
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
  letter-spacing: 0.5rpx;
}

.tab-item.active .tab-text {
  color: var(--accent-500);
  font-weight: 600;
  text-shadow: 0 0 4rpx rgba(23, 224, 255, 0.5);
  animation: tab-text-glow 2s ease-in-out infinite alternate;
}

@keyframes tab-text-glow {
  0% { text-shadow: 0 0 4rpx rgba(23, 224, 255, 0.5); }
  100% { text-shadow: 0 0 8rpx rgba(23, 224, 255, 0.8); }
}

.tab-item:hover .tab-text {
  color: var(--text-secondary);
  transform: translateY(-1rpx);
}

/* 活跃指示器 */
.tab-indicator {
  position: absolute;
  top: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background: var(--accent-500);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: 0 0 8rpx var(--accent-500);
}

.tab-item.active .tab-indicator {
  opacity: 1;
  animation: indicator-pulse 1.5s ease-in-out infinite;
}

@keyframes indicator-pulse {
  0%, 100% { 
    transform: translateX(-50%) scale(1);
    opacity: 1;
  }
  50% { 
    transform: translateX(-50%) scale(1.3);
    opacity: 0.7;
  }
}

/* 底部安全区域适配 */
.tab-safe-area {
  height: env(safe-area-inset-bottom);
  background: inherit;
}

/* 响应式优化 */
@media screen and (max-width: 320px) {
  .custom-tab-bar {
    height: calc(80rpx + env(safe-area-inset-bottom));
  }
  
  .tab-bar-content {
    height: 80rpx;
    padding: 0 var(--spacing-sm);
  }
  
  .tab-item {
    margin: 0 var(--spacing-xs);
  }
  
  .tab-icon {
    width: 36rpx;
    height: 36rpx;
  }
  
  .tab-text {
    font-size: 18rpx;
  }
  
  .tab-icon-container {
    margin-bottom: 4rpx;
  }
}

@media screen and (max-width: 375px) {
  .tab-bar-content {
    padding: 0 var(--spacing-md);
  }
  
  .tab-icon {
    width: 40rpx;
    height: 40rpx;
  }
  
  .tab-text {
    font-size: 19rpx;
  }
}

@media screen and (min-width: 414px) {
  .custom-tab-bar {
    height: calc(110rpx + env(safe-area-inset-bottom));
  }
  
  .tab-bar-content {
    height: 110rpx;
    padding: 0 var(--spacing-xl);
  }
  
  .tab-icon {
    width: 48rpx;
    height: 48rpx;
  }
  
  .tab-text {
    font-size: 22rpx;
  }
  
  .tab-icon-container {
    margin-bottom: var(--spacing-md);
  }
}

@media screen and (min-width: 500px) {
  .custom-tab-bar {
    height: calc(120rpx + env(safe-area-inset-bottom));
  }
  
  .tab-bar-content {
    height: 120rpx;
    padding: 0 60rpx;
  }
  
  .tab-item {
    margin: 0 var(--spacing-sm);
    border-radius: var(--border-radius);
  }
  
  .tab-icon {
    width: 52rpx;
    height: 52rpx;
  }
  
  .tab-text {
    font-size: 24rpx;
  }
  
  .tab-icon-container {
    margin-bottom: var(--spacing-lg);
  }
}

/* 横屏模式适配 */
@media screen and (orientation: landscape) {
  .custom-tab-bar {
    height: calc(80rpx + env(safe-area-inset-bottom));
  }
  
  .tab-bar-content {
    height: 80rpx;
    padding: 0 var(--spacing-xl);
  }
  
  .tab-item {
    flex-direction: row;
    gap: var(--spacing-sm);
    margin: 0 var(--spacing-md);
  }
  
  .tab-icon-container {
    margin-bottom: 0;
  }
  
  .tab-icon {
    width: 40rpx;
    height: 40rpx;
  }
  
  .tab-text {
    font-size: 20rpx;
  }
  
  .tab-indicator {
    top: 50%;
    left: 8rpx;
    transform: translateY(-50%);
  }
  
  .tab-item.active .tab-indicator {
    animation: indicator-pulse-horizontal 1.5s ease-in-out infinite;
  }
}

@keyframes indicator-pulse-horizontal {
  0%, 100% { 
    transform: translateY(-50%) scale(1);
    opacity: 1;
  }
  50% { 
    transform: translateY(-50%) scale(1.3);
    opacity: 0.7;
  }
}

/* iPhone X系列及以上设备优化 */
@media screen and (device-width: 375px) and (device-height: 812px),
       screen and (device-width: 414px) and (device-height: 896px),
       screen and (device-width: 390px) and (device-height: 844px),
       screen and (device-width: 428px) and (device-height: 926px) {
  .custom-tab-bar {
    background: rgba(21, 21, 31, 0.9);
    backdrop-filter: blur(var(--blur-strong));
  }
  
  .custom-tab-bar::before {
    opacity: 0.8;
  }
}

/* 深色模式增强 */
@media (prefers-color-scheme: dark) {
  .custom-tab-bar {
    background: rgba(11, 11, 17, 0.9);
    border-top-color: rgba(255, 255, 255, 0.1);
  }
  
  .tab-item.active {
    background: linear-gradient(135deg, rgba(23, 224, 255, 0.2) 0%, rgba(108, 93, 211, 0.1) 100%);
    border-color: rgba(23, 224, 255, 0.3);
    box-shadow: 0 0 20rpx rgba(23, 224, 255, 0.3);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .tab-item:hover {
    background: none;
    transform: none;
  }
  
  .tab-item:hover .tab-icon {
    filter: brightness(0.7);
    transform: none;
  }
  
  .tab-item:hover .tab-text {
    color: var(--text-tertiary);
    transform: none;
  }
  
  .tab-item:active {
    background: var(--glass-active);
    transform: scale(0.98);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .tab-item,
  .tab-icon,
  .tab-text,
  .tab-indicator,
  .tab-icon-container,
  .custom-tab-bar,
  .custom-tab-bar::before {
    animation: none !important;
    transition: none !important;
  }
  
  .tab-item.active .tab-icon-container {
    animation: none;
  }
  
  .tab-item.active .tab-text {
    animation: none;
  }
  
  .tab-item.active .tab-indicator {
    animation: none;
  }
}