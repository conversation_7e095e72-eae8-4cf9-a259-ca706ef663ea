/* pages/test-adaptation/test-adaptation.wxss */
.container {
  padding: calc(env(safe-area-inset-top) + 40rpx) clamp(32rpx, 6vw, 48rpx) calc(env(safe-area-inset-bottom) + 150rpx);
  min-height: 100vh;
  max-width: 750rpx;
  margin: 0 auto;
}

.title {
  font-size: clamp(48rpx, 9vw, 64rpx);
  font-weight: 600;
  color: var(--text-glow);
  text-align: center;
  margin-bottom: var(--spacing-xl);
  text-shadow: var(--shadow-neon-sm);
}

.section-title {
  font-size: clamp(32rpx, 6vw, 40rpx);
  font-weight: 500;
  color: var(--primary-400);
  margin-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--glass-border);
  padding-bottom: var(--spacing-sm);
}

.info-item,
.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.info-item:last-child,
.test-item:last-child {
  border-bottom: none;
}

.label,
.test-label {
  font-size: clamp(24rpx, 4.5vw, 28rpx);
  color: var(--text-secondary);
  flex: 1;
}

.value,
.test-value {
  font-size: clamp(24rpx, 4.5vw, 28rpx);
  color: var(--text-primary);
  font-weight: 500;
  text-align: right;
  flex: 1;
}

.test-results {
  max-height: 400rpx;
  overflow-y: auto;
}

.visual-test {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: var(--spacing-lg) 0;
  gap: var(--spacing-md);
}

.test-box {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius);
  background: var(--glass-hover);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  font-weight: 500;
  transition: all var(--transition-smooth);
}

.test-box.small {
  width: clamp(60rpx, 12vw, 80rpx);
  height: clamp(60rpx, 12vw, 80rpx);
  font-size: clamp(20rpx, 4vw, 24rpx);
}

.test-box.medium {
  width: clamp(80rpx, 16vw, 100rpx);
  height: clamp(80rpx, 16vw, 100rpx);
  font-size: clamp(24rpx, 5vw, 28rpx);
}

.test-box.large {
  width: clamp(100rpx, 20vw, 120rpx);
  height: clamp(100rpx, 20vw, 120rpx);
  font-size: clamp(28rpx, 6vw, 32rpx);
}

.test-box:hover {
  background: var(--glass-focus);
  border-color: var(--primary-400);
  transform: scale(1.05);
}

.test-text {
  font-size: clamp(24rpx, 4.5vw, 28rpx);
  line-height: 1.6;
  color: var(--text-secondary);
  text-align: justify;
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--glass-bg);
  border-radius: var(--border-radius-input);
  border: 1px solid var(--glass-border);
}

.actions {
  margin-top: var(--spacing-xl);
}

/* 设备特定适配 */
.device-small .container {
  padding-left: clamp(16rpx, 4vw, 24rpx);
  padding-right: clamp(16rpx, 4vw, 24rpx);
}

.device-small .visual-test {
  flex-direction: column;
  gap: var(--spacing-sm);
}

.device-tablet .container {
  max-width: 900rpx;
}

.device-tablet .visual-test {
  gap: var(--spacing-xl);
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
  .container {
    padding-top: calc(env(safe-area-inset-top) + 20rpx);
    padding-bottom: calc(env(safe-area-inset-bottom) + 80rpx);
  }
  
  .title {
    font-size: clamp(40rpx, 7vw, 52rpx);
    margin-bottom: var(--spacing-md);
  }
  
  .visual-test {
    margin: var(--spacing-md) 0;
  }
}
