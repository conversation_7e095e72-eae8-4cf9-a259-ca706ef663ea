/* pages/wishes/wishes.wxss - 梦想量子版 */
.container {
  padding: calc(env(safe-area-inset-top) + 50rpx) clamp(40rpx, 8vw, 60rpx) calc(env(safe-area-inset-bottom) + 150rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  max-width: 750rpx;
  margin: 0 auto;
  animation: wishes-materialize 1s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 愿望物质化动画 */
@keyframes wishes-materialize {
  0% {
    opacity: 0;
    transform: scale(0.95) rotateX(15deg);
    filter: blur(8rpx);
  }
  70% {
    opacity: 0.9;
    transform: scale(1.02) rotateX(0deg);
    filter: blur(1rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotateX(0deg);
    filter: blur(0);
  }
}

/* 页面标题 */
.page-title {
  font-size: clamp(48rpx, 10vw, 64rpx);
  font-weight: 200;
  margin-bottom: 60rpx;
  text-align: center;
  letter-spacing: 6rpx;
  background: linear-gradient(135deg, 
    #00D9FF 0%, 
    #8A2BE2 35%, 
    #FF0080 70%, 
    #00D9FF 100%);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: title-flow 4s ease-in-out infinite;
  position: relative;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -16rpx;
  left: 50%;
  width: 120rpx;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #00D9FF, transparent);
  transform: translateX(-50%);
  animation: title-line-glow 3s ease-in-out infinite;
}

@keyframes title-flow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes title-line-glow {
  0%, 100% {
    opacity: 0.5;
    width: 120rpx;
  }
  50% {
    opacity: 1;
    width: 200rpx;
  }
}

/* 添加愿望区域 */
.add-section {
  width: 100%;
  margin-bottom: 60rpx;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(0, 217, 255, 0.15);
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  backdrop-filter: blur(30rpx);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 8rpx 40rpx rgba(0, 0, 0, 0.2),
    0 0 60rpx rgba(0, 217, 255, 0.1);
  transition: all 0.5s ease;
}

.add-section::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
    rgba(0, 217, 255, 0.02) 0%,
    transparent 50%,
    rgba(138, 43, 226, 0.015) 100%);
  pointer-events: none;
}

.add-section:hover {
  border-color: rgba(0, 217, 255, 0.25);
  box-shadow: 
    0 12rpx 50rpx rgba(0, 0, 0, 0.25),
    0 0 80rpx rgba(0, 217, 255, 0.15);
}

.add-form {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.input-group {
  position: relative;
}

.input-label {
  display: block;
  font-size: 28rpx;
  font-weight: 400;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
  opacity: 0.9;
}

.wish-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid rgba(0, 217, 255, 0.1);
  border-radius: 16rpx;
  padding: 28rpx 24rpx;
  color: var(--text-primary);
  font-size: 30rpx;
  font-weight: 300;
  transition: all 0.4s ease;
  backdrop-filter: blur(10rpx);
  box-sizing: border-box;
  line-height: 1.5;
}

.wish-input:focus {
  border-color: #00D9FF;
  background: rgba(0, 217, 255, 0.05);
  outline: none;
  box-shadow: 
    0 0 0 4rpx rgba(0, 217, 255, 0.1),
    0 0 40rpx rgba(0, 217, 255, 0.2);
  transform: scale(1.01);
}

.wish-input::placeholder {
  color: var(--text-tertiary);
  font-style: italic;
  opacity: 0.7;
}

.textarea {
  min-height: 120rpx;
  resize: none;
}

.add-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, 
    #00D9FF 0%, 
    #8A2BE2 50%, 
    #FF0080 100%);
  border: none;
  border-radius: 20rpx;
  color: #FFFFFF;
  font-weight: 500;
  font-size: 32rpx;
  letter-spacing: 3rpx;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 
    0 8rpx 30rpx rgba(0, 217, 255, 0.3),
    0 0 60rpx rgba(0, 217, 255, 0.2);
}

.add-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    transparent 50%, 
    rgba(255, 255, 255, 0.1) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.add-btn:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 
    0 15rpx 50rpx rgba(0, 217, 255, 0.4),
    0 0 80rpx rgba(138, 43, 226, 0.3);
}

.add-btn:hover::before {
  transform: translateX(100%);
}

.add-btn:active {
  transform: translateY(-2rpx) scale(1.01);
}

.add-btn:disabled {
  background: linear-gradient(135deg, #2A2A2A, #1A1A1A);
  color: #666666;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* 愿望列表 */
.wishes-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
  font-weight: 300;
  letter-spacing: 1rpx;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.015);
  border: 1px dashed rgba(0, 217, 255, 0.2);
  border-radius: 20rpx;
  backdrop-filter: blur(20rpx);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
  filter: grayscale(1);
}

/* 愿望卡片 */
.wish-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(0, 217, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(25rpx);
  box-shadow: 
    0 8rpx 32rpx rgba(0, 0, 0, 0.15),
    0 0 60rpx rgba(0, 217, 255, 0.05);
  animation: wish-card-enter 0.6s ease-out backwards;
}

@keyframes wish-card-enter {
  0% {
    opacity: 0;
    transform: translateY(40rpx) scale(0.95);
    filter: blur(4rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.wish-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
    rgba(0, 217, 255, 0.02) 0%,
    transparent 40%,
    rgba(138, 43, 226, 0.015) 60%,
    transparent 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.wish-card:hover {
  border-color: rgba(0, 217, 255, 0.25);
  transform: translateY(-6rpx) scale(1.01);
  box-shadow: 
    0 15rpx 50rpx rgba(0, 0, 0, 0.2),
    0 0 80rpx rgba(0, 217, 255, 0.1);
}

.wish-card:hover::before {
  opacity: 1;
}

.wish-card.completed {
  border-color: rgba(0, 255, 128, 0.3);
  background: rgba(0, 255, 128, 0.02);
}

.wish-card.completed::before {
  background: linear-gradient(135deg,
    rgba(0, 255, 128, 0.03) 0%,
    transparent 40%,
    rgba(0, 255, 128, 0.02) 60%,
    transparent 100%);
}

/* 愿望内容 */
.wish-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.wish-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  letter-spacing: 1rpx;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.wish-card.completed .wish-title {
  color: #00FF80;
  text-decoration: line-through;
  opacity: 0.8;
}

.wish-description {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  font-weight: 300;
  opacity: 0.9;
}

.wish-card.completed .wish-description {
  opacity: 0.6;
}

.wish-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid rgba(0, 217, 255, 0.1);
}

.wish-date {
  font-size: 22rpx;
  color: var(--text-tertiary);
  font-weight: 300;
  opacity: 0.7;
}

.wish-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  letter-spacing: 1rpx;
}

.complete-btn {
  background: rgba(0, 255, 128, 0.1);
  border: 1px solid rgba(0, 255, 128, 0.3);
  color: #00FF80;
}

.complete-btn:hover {
  background: rgba(0, 255, 128, 0.2);
  border-color: rgba(0, 255, 128, 0.5);
  transform: scale(1.05);
  box-shadow: 0 0 20rpx rgba(0, 255, 128, 0.3);
}

.delete-btn {
  background: rgba(255, 0, 128, 0.1);
  border: 1px solid rgba(255, 0, 128, 0.3);
  color: #FF0080;
}

.delete-btn:hover {
  background: rgba(255, 0, 128, 0.2);
  border-color: rgba(255, 0, 128, 0.5);
  transform: scale(1.05);
  box-shadow: 0 0 20rpx rgba(255, 0, 128, 0.3);
}

/* 统计信息 */
.stats-section {
  width: 100%;
  margin-top: 60rpx;
  padding: 40rpx 30rpx;
  background: rgba(255, 255, 255, 0.015);
  border: 1px solid rgba(0, 217, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(25rpx);
  text-align: center;
  box-shadow: 
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    0 0 60rpx rgba(0, 217, 255, 0.05);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.stat-number {
  font-size: clamp(36rpx, 8vw, 48rpx);
  font-weight: 600;
  background: linear-gradient(135deg, 
    #00D9FF 0%, 
    #8A2BE2 50%, 
    #FF0080 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 2rpx;
}

.stat-label {
  font-size: 22rpx;
  color: var(--text-secondary);
  font-weight: 300;
  opacity: 0.8;
  letter-spacing: 1rpx;
}

/* 响应式优化 */
@media (max-width: 400px) {
  .container {
    padding-left: 30rpx;
    padding-right: 30rpx;
  }
  
  .page-title {
    font-size: 42rpx;
    letter-spacing: 4rpx;
    margin-bottom: 40rpx;
  }
  
  .add-section,
  .wish-card {
    padding: 30rpx 20rpx;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }
  
  .wish-actions {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .action-btn {
    width: 100%;
    text-align: center;
  }
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40rpx;
  height: 40rpx;
  margin: -20rpx 0 0 -20rpx;
  border: 4rpx solid rgba(0, 217, 255, 0.3);
  border-top: 4rpx solid #00D9FF;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 成功状态动画 */
.wish-added {
  animation: wish-success 0.8s ease-out;
}

@keyframes wish-success {
  0% {
    transform: scale(1);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 
      0 15rpx 50rpx rgba(0, 217, 255, 0.3),
      0 0 80rpx rgba(0, 217, 255, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  }
}

/* 删除动画 */
.wish-removing {
  animation: wish-remove 0.5s ease-out forwards;
}

@keyframes wish-remove {
  0% {
    opacity: 1;
    transform: scale(1) translateX(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) translateX(-100%);
  }
} 