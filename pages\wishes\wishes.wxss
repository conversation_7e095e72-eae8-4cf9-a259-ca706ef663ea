/* pages/wishes/wishes.wxss - 简洁版 */
.container {
  padding: calc(env(safe-area-inset-top) + 40rpx) clamp(32rpx, 6vw, 48rpx) calc(env(safe-area-inset-bottom) + 150rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  max-width: 750rpx;
  margin: 0 auto;
}

/* 页面标题 - 简洁版 */
.page-title {
  font-size: clamp(44rpx, 9vw, 56rpx);
  font-weight: 600;
  margin-bottom: var(--spacing-xl);
  text-align: center;
  letter-spacing: 1rpx;
  color: var(--text-primary);
}

/* 添加愿望区域 - 简洁版 */
.add-section {
  width: 100%;
  margin-bottom: var(--spacing-xl);
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.add-section:hover {
  background: var(--card-hover);
  box-shadow: var(--shadow-md);
}

.add-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.input-group {
  position: relative;
}

.input-label {
  display: block;
  font-size: 26rpx;
  font-weight: 400;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.wish-input {
  width: 100%;
  background: var(--surface-primary);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius-input);
  padding: var(--spacing-md);
  color: var(--text-primary);
  font-size: 28rpx;
  font-weight: 400;
  transition: all var(--transition-fast);
  box-sizing: border-box;
  line-height: 1.5;
}

.wish-input:focus {
  border-color: var(--primary-400);
  background: var(--card-hover);
  outline: none;
  box-shadow: 0 0 0 2rpx rgba(0, 180, 219, 0.2);
}

.wish-input::placeholder {
  color: var(--text-tertiary);
}

.textarea {
  min-height: 120rpx;
  resize: none;
}

.add-btn {
  width: 100%;
  height: 88rpx;
  background: var(--primary-500);
  border: none;
  border-radius: var(--border-radius-btn);
  color: var(--text-primary);
  font-weight: 500;
  font-size: 30rpx;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.add-btn:hover {
  background: var(--primary-400);
  box-shadow: var(--shadow-md);
}

.add-btn:active {
  transform: scale(0.98);
}

.add-btn:disabled {
  background: var(--surface-secondary);
  color: var(--text-muted);
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.6;
}

/* 愿望列表 */
.wishes-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
  font-size: 26rpx;
  font-weight: 400;
  background: var(--card-bg);
  border: 1px dashed var(--card-border);
  border-radius: var(--border-radius);
}

.empty-icon {
  font-size: 64rpx;
  margin-bottom: var(--spacing-lg);
  opacity: 0.6;
}

/* 愿望卡片 - 简洁版 */
.wish-card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.wish-card:hover {
  background: var(--card-hover);
  box-shadow: var(--shadow-md);
}

.wish-card.completed {
  border-color: var(--secondary-400);
  background: var(--surface-tertiary);
}

/* 愿望内容 */
.wish-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.wish-title {
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: var(--spacing-sm);
}

.wish-card.completed .wish-title {
  color: var(--secondary-400);
  text-decoration: line-through;
  opacity: 0.7;
}

.wish-description {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  font-weight: 400;
}

.wish-card.completed .wish-description {
  opacity: 0.6;
}

.wish-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--card-border);
}

.wish-date {
  font-size: 20rpx;
  color: var(--text-tertiary);
  font-weight: 400;
}

.wish-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-btn);
  font-size: 22rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.complete-btn {
  background: var(--secondary-500);
  color: var(--text-primary);
}

.complete-btn:hover {
  background: var(--secondary-400);
}

.delete-btn {
  background: var(--surface-secondary);
  color: var(--text-muted);
  border: 1px solid var(--card-border);
}

.delete-btn:hover {
  background: var(--card-hover);
  color: var(--text-primary);
}

/* 统计信息 */
.stats-section {
  width: 100%;
  margin-top: 60rpx;
  padding: 40rpx 30rpx;
  background: rgba(255, 255, 255, 0.015);
  border: 1px solid rgba(0, 217, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(25rpx);
  text-align: center;
  box-shadow: 
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    0 0 60rpx rgba(0, 217, 255, 0.05);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.stat-number {
  font-size: clamp(36rpx, 8vw, 48rpx);
  font-weight: 600;
  background: linear-gradient(135deg, 
    #00D9FF 0%, 
    #8A2BE2 50%, 
    #FF0080 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 2rpx;
}

.stat-label {
  font-size: 22rpx;
  color: var(--text-secondary);
  font-weight: 300;
  opacity: 0.8;
  letter-spacing: 1rpx;
}

/* 响应式优化 */
@media (max-width: 400px) {
  .container {
    padding-left: 30rpx;
    padding-right: 30rpx;
  }
  
  .page-title {
    font-size: 42rpx;
    letter-spacing: 4rpx;
    margin-bottom: 40rpx;
  }
  
  .add-section,
  .wish-card {
    padding: 30rpx 20rpx;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }
  
  .wish-actions {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .action-btn {
    width: 100%;
    text-align: center;
  }
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40rpx;
  height: 40rpx;
  margin: -20rpx 0 0 -20rpx;
  border: 4rpx solid rgba(0, 217, 255, 0.3);
  border-top: 4rpx solid #00D9FF;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 成功状态动画 */
.wish-added {
  animation: wish-success 0.8s ease-out;
}

@keyframes wish-success {
  0% {
    transform: scale(1);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 
      0 15rpx 50rpx rgba(0, 217, 255, 0.3),
      0 0 80rpx rgba(0, 217, 255, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  }
}

/* 删除动画 */
.wish-removing {
  animation: wish-remove 0.5s ease-out forwards;
}

@keyframes wish-remove {
  0% {
    opacity: 1;
    transform: scale(1) translateX(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) translateX(-100%);
  }
} 