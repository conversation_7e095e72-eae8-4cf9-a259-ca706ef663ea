<cover-view class="tab-bar {{deviceClassName}}">
  <cover-view class="tab-bar-border"></cover-view>
  <cover-view 
    wx:for="{{list}}" 
    wx:key="index" 
    class="tab-bar-item {{selected === index ? 'tab-bar-item-active' : ''}}" 
    data-path="{{item.pagePath}}" 
    data-index="{{index}}" 
    bindtap="switchTab"
  >
    <cover-view class="tab-bar-item-text">{{item.text}}</cover-view>
  </cover-view>
</cover-view> 