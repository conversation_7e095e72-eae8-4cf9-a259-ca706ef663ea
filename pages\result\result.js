// pages/result/result.js
import { getDaysBetween } from '../../utils/dateUtils';
import { getQuote } from '../../utils/quotes';
const deviceUtils = require('../../utils/deviceUtils.js');

Page({
  data: {
    birth: '',
    targetDays: 30000,
    usedDays: 0,
    remainingDays: 0,
    percentUsed: 0,
    percentText: '0.0',
    dailyQuote: '',
    showImportantReminder: false,
    currentDate: '',
    currentTime: '',
    timer: null
  },
  
  onLoad: function (options) {
    if (!options.birth) {
      wx.showToast({
        title: '参数错误',
        icon: 'none',
        complete: () => {
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      });
      return;
    }

    // 获取出生日期和目标寿命天数
    const birth = options.birth;
    const targetDays = parseInt(options.targetDays || 30000);

    // 使用工具函数计算已使用天数
    const usedDays = getDaysBetween(birth);
    const remainingDays = targetDays - usedDays;

    // 计算使用百分比
    const percentUsed = usedDays / targetDays;
    const percentText = (percentUsed * 100).toFixed(1);

    // 检查是否是重要节点
    const isImportantMilestone = this.checkImportantMilestone(remainingDays);

    // 获取设备适配信息
    const app = getApp();
    const deviceClassName = app.globalData.deviceClassName || '';
    const adaptiveParams = app.globalData.adaptiveParams || {};

    // 获取当前日期和时间
    this.updateCurrentDateTime();

    // 设置定时器，每秒更新一次时间
    const timer = setInterval(() => {
      this.updateCurrentDateTime();
    }, 1000);

    this.setData({
      birth,
      targetDays,
      usedDays,
      remainingDays,
      percentUsed,
      percentText,
      dailyQuote: getQuote(),
      showImportantReminder: isImportantMilestone,
      timer,
      deviceClassName: deviceClassName,
      adaptiveParams: adaptiveParams
    });

    // 在下一个渲染周期绘制环形进度条
    wx.nextTick(() => {
      this.drawLifeRing();
    });
  },
  
  // 页面卸载时清除定时器
  onUnload: function() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },
  
  // 更新当前日期和时间
  updateCurrentDateTime: function() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    const dateStr = `${year}年${month}月${day}日`;
    const timeStr = `${hours}:${minutes}:${seconds}`;
    
    this.setData({
      currentDate: dateStr,
      currentTime: timeStr
    });
  },
  
  /**
   * 检查给定的剩余天数是否是重要节点
   * @param {number} remainingDays 剩余天数
   * @returns {boolean} 是否为重要节点
   * @private
   */
  checkImportantMilestone: function(remainingDays) {
    const importantMilestones = [10000, 5000, 1000];
    return importantMilestones.includes(remainingDays);
  },
  
  /**
   * 绘制环形进度条
   * 使用canvas 2D绘制一个动画的环形进度条，表示生命使用百分比
   * @private
   */
  drawLifeRing: function() {
    const query = wx.createSelectorQuery();
    query.select('#lifeRing')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0] || !res[0].node) {
          console.error('Canvas节点获取失败');
          return;
        }
        
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        
        // 获取Canvas适配参数
        const adaptParams = deviceUtils.getCanvasAdaptParams(res[0]);
        canvas.width = adaptParams.canvasWidth;
        canvas.height = adaptParams.canvasHeight;
        ctx.scale(adaptParams.dpr, adaptParams.dpr);

        const width = adaptParams.width;
        const height = adaptParams.height;
        const centerX = width / 2;
        const centerY = height / 2;

        // 环形参数 - 根据设备类型自适应
        const baseRadius = deviceUtils.getResponsiveSize({
          small: 100,
          medium: 120,
          large: 140,
          tablet: 160
        });
        const baseLineWidth = deviceUtils.getResponsiveSize({
          small: 12,
          medium: 16,
          large: 18,
          tablet: 20
        });

        const radius = baseRadius * adaptParams.contentScale;
        const lineWidth = baseLineWidth * adaptParams.contentScale;
        const startAngle = -0.5 * Math.PI; // 12点方向开始
        const percent = this.data.percentUsed;
        
        // 动画参数
        const duration = 1500; // 动画时长1500ms
        const startTime = Date.now();
        
        // 清除画布
        ctx.clearRect(0, 0, width, height);
        
        // 动画函数
        const animate = () => {
          const now = Date.now();
          const progress = Math.min(1, (now - startTime) / duration);
          const currentPercent = progress * percent;
          
          // 清除画布
          ctx.clearRect(0, 0, width, height);
          
          // 绘制发光外环
          ctx.beginPath();
          ctx.arc(centerX, centerY, radius + 5, 0, 2 * Math.PI);
          ctx.lineWidth = 2;
          ctx.strokeStyle = 'rgba(93, 156, 255, 0.2)';
          ctx.stroke();
          
          // 绘制背景环
          ctx.beginPath();
          ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
          ctx.lineWidth = lineWidth;
          ctx.strokeStyle = 'rgba(45, 66, 120, 0.3)';
          ctx.stroke();
          
          // 绘制进度环
          ctx.beginPath();
          ctx.arc(centerX, centerY, radius, startAngle, startAngle + currentPercent * 2 * Math.PI);
          ctx.lineWidth = lineWidth;
          
          // 渐变色进度条
          const gradient = ctx.createLinearGradient(
            centerX - radius, centerY, centerX + radius, centerY
          );
          gradient.addColorStop(0, '#1060ff');
          gradient.addColorStop(1, '#30a5ff');
          
          ctx.strokeStyle = gradient;
          ctx.lineCap = 'round';
          ctx.stroke();
          
          // 绘制中心文字（百分比）- 自适应字体大小
          const fontSize = deviceUtils.getResponsiveSize({
            small: 36,
            medium: 48,
            large: 54,
            tablet: 60
          }) * adaptParams.contentScale;

          ctx.fillStyle = '#5d9cff';
          ctx.font = `bold ${fontSize}px -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.shadowColor = 'rgba(93, 156, 255, 0.8)';
          ctx.shadowBlur = 12 * adaptParams.contentScale;
          ctx.fillText(`${(currentPercent * 100).toFixed(1)}%`, centerX, centerY);
          ctx.shadowBlur = 0;
          
          // 绘制发光效果
          ctx.shadowColor = 'rgba(93, 156, 255, 0.8)';
          ctx.shadowBlur = 15;
          ctx.strokeStyle = 'rgba(93, 156, 255, 0.1)';
          ctx.beginPath();
          ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
          ctx.stroke();
          ctx.shadowBlur = 0;
          
          // 继续动画或结束
          if (progress < 1) {
            canvas.requestAnimationFrame(animate);
          }
        };
        
        // 启动动画
        canvas.requestAnimationFrame(animate);
      });
  },
  
  /**
   * 生成可分享的图片
   * 创建一个包含用户生命统计数据的图片，并保存到相册
   * @private
   */
  generateShareImage: function() {
    wx.showLoading({
      title: '生成图片中...',
    });
    
    const query = wx.createSelectorQuery();
    query.select('#shareCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0] || !res[0].node) {
          console.error('Canvas节点获取失败');
          wx.hideLoading();
          return;
        }
        
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        
        // 设置画布尺寸 - 根据设备类型调整
        const shareCanvasSize = deviceUtils.getResponsiveSize({
          small: { width: 1080, height: 1920 },
          medium: { width: 1080, height: 1920 },
          large: { width: 1200, height: 2133 },
          tablet: { width: 1440, height: 2560 }
        });

        canvas.width = shareCanvasSize.width || 1080;
        canvas.height = shareCanvasSize.height || 1920;
        
        // 绘制背景
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, '#030518');
        gradient.addColorStop(1, '#0a1745');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // 计算字体大小比例
        const fontScale = canvas.width / 1080;

        // 绘制头部信息
        ctx.fillStyle = '#5d9cff';
        ctx.font = `bold ${80 * fontScale}px sans-serif`;
        ctx.textAlign = 'center';
        ctx.shadowColor = 'rgba(93, 156, 255, 0.8)';
        ctx.shadowBlur = 20 * fontScale;
        ctx.fillText('倒数人生', canvas.width / 2, 200 * fontScale);
        ctx.shadowBlur = 0;

        // 绘制当前日期
        ctx.fillStyle = '#ffffff';
        ctx.font = `${40 * fontScale}px sans-serif`;
        ctx.fillText(this.data.currentDate, canvas.width / 2, 300 * fontScale);
        
        // 绘制剩余天数
        ctx.fillStyle = '#5d9cff';
        ctx.font = 'bold 180px sans-serif';
        ctx.shadowColor = 'rgba(93, 156, 255, 0.6)';
        ctx.shadowBlur = 25;
        ctx.fillText(this.data.remainingDays, canvas.width / 2, 600);
        ctx.shadowBlur = 0;
        
        ctx.fillStyle = '#ffffff';
        ctx.font = '60px sans-serif';
        ctx.fillText('剩余天数', canvas.width / 2, 700);
        
        // 绘制进度信息
        ctx.fillStyle = '#ffffff';
        ctx.font = '50px sans-serif';
        ctx.fillText(`已使用 ${this.data.percentText}% 的生命`, canvas.width / 2, 850);
        
        // 绘制分隔线
        ctx.strokeStyle = 'rgba(93, 156, 255, 0.3)';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(canvas.width / 2 - 300, 950);
        ctx.lineTo(canvas.width / 2 + 300, 950);
        ctx.stroke();
        
        // 绘制金句
        ctx.fillStyle = '#d0d7ff';
        ctx.font = 'italic 50px sans-serif';
        
        // 处理长句子换行
        const quote = this.data.dailyQuote;
        const maxWidth = 900;
        let words = quote.split('');
        let line = '';
        let lines = [];
        let y = 1100;
        
        for (let i = 0; i < words.length; i++) {
          let testLine = line + words[i];
          let metrics = ctx.measureText(testLine);
          let testWidth = metrics.width;
          
          if (testWidth > maxWidth && i > 0) {
            lines.push(line);
            line = words[i];
          } else {
            line = testLine;
          }
        }
        lines.push(line);
        
        // 绘制多行文本
        for (let i = 0; i < lines.length; i++) {
          ctx.fillText(lines[i], canvas.width / 2, y);
          y += 80;
        }
        
        // 绘制小程序码区域
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.fillRect(canvas.width / 2 - 80, 1400, 160, 160);
        ctx.strokeStyle = 'rgba(93, 156, 255, 0.5)';
        ctx.lineWidth = 2;
        ctx.strokeRect(canvas.width / 2 - 80, 1400, 160, 160);
        
        // 绘制小程序码中心的图标
        ctx.fillStyle = '#5d9cff';
        ctx.font = 'bold 80px sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('🎯', canvas.width / 2, 1480);
        
        // 绘制底部信息
        ctx.fillStyle = '#ffffff';
        ctx.font = '40px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('微信搜索倒数人生，查看您的生命进度', canvas.width / 2, 1650);
        
        // 将画布转换为临时文件路径
        setTimeout(() => {
          wx.canvasToTempFilePath({
            canvas: canvas,
            success: (res) => {
              // 保存图片到相册
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  wx.hideLoading();
                  wx.showToast({
                    title: '图片已保存相册',
                    icon: 'success'
                  });
                },
                fail: (err) => {
                  wx.hideLoading();
                  console.error('保存失败', err);
                  
                  // 用户拒绝授权
                  if (err.errMsg.indexOf('auth deny') >= 0) {
                    wx.showModal({
                      title: '提示',
                      content: '需要您授权保存图片到相册',
                      showCancel: false
                    });
                  } else {
                    wx.showToast({
                      title: '保存失败',
                      icon: 'none'
                    });
                  }
                }
              });
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('生成图片失败', err);
              wx.showToast({
                title: '生成图片失败',
                icon: 'none'
              });
            }
          }, this);
        }, 200);
      });
  },
  
  backToHome: function() {
    wx.navigateBack({
      delta: 1
    });
  }
}) 