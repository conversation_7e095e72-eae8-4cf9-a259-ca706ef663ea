/**app.wxss**/
/* Design Tokens - 全局设计变量 - 升级版 */
:root {
  /* 背景色系 - 深空科技 */
  --bg-900: #0A0A0F;
  --bg-800: #121218;
  --surface-800: #18181F;
  --surface-700: #1F1F28;
  --surface-600: #28283A;
  
  /* 品牌色系 - 量子发光 */
  --primary-500: #00D9FF;
  --primary-400: #4DE6FF;
  --primary-300: #80EFFF;
  --accent-500: #FF0080;
  --accent-400: #FF4DA6;
  --secondary-500: #8A2BE2;
  --secondary-400: #B347FF;
  
  /* 文字色系 - 未来字体 */
  --text-primary: #FFFFFF;
  --text-secondary: #C4C4D6;
  --text-tertiary: #8A8A9E;
  --text-muted: #5A5A6B;
  --text-glow: #00D9FF;
  
  /* 霓虹玻璃拟态 */
  --glass-bg: rgba(255, 255, 255, 0.03);
  --glass-border: rgba(0, 217, 255, 0.15);
  --glass-hover: rgba(0, 217, 255, 0.08);
  --glass-active: rgba(255, 0, 128, 0.06);
  --glass-focus: rgba(0, 217, 255, 0.12);
  
  /* 动态效果 */
  --blur: 20px;
  --blur-strong: 30px;
  --border-radius: 20rpx;
  --border-radius-btn: 25rpx;
  --border-radius-input: 18rpx;
  --transition-smooth: 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* 间距 - 响应式 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 36rpx;
  --spacing-xl: 48rpx;
  --spacing-2xl: 64rpx;
  --spacing-3xl: 80rpx;
  
  /* 霓虹阴影 */
  --shadow-neon-sm: 0 0 20rpx rgba(0, 217, 255, 0.3);
  --shadow-neon-md: 0 0 40rpx rgba(0, 217, 255, 0.4);
  --shadow-neon-lg: 0 0 60rpx rgba(0, 217, 255, 0.5);
  --shadow-pink-sm: 0 0 20rpx rgba(255, 0, 128, 0.3);
  --shadow-pink-md: 0 0 40rpx rgba(255, 0, 128, 0.4);
  --shadow-purple-sm: 0 0 20rpx rgba(138, 43, 226, 0.3);
}

/* 全局页面样式 - 增强版 */
page {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: var(--bg-900);
  color: var(--text-primary);
  width: 100vw;
  min-height: 100vh;
  overflow-x: hidden;
  line-height: 1.5;
  font-weight: 300;
  letter-spacing: 0.5rpx;
}

/* 量子空间背景 */
.page-bg {
  position: fixed;
  inset: 0;
  background:
    radial-gradient(ellipse at 20% 30%, rgba(0, 217, 255, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 70%, rgba(255, 0, 128, 0.12) 0%, transparent 45%),
    radial-gradient(ellipse at 50% 20%, rgba(138, 43, 226, 0.08) 0%, transparent 60%),
    linear-gradient(180deg, var(--bg-900) 0%, var(--bg-800) 100%);
  z-index: -10;
}

/* 动态粒子场 */
.page-bg::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 15% 25%, rgba(0, 217, 255, 0.8) 1px, transparent 1px),
    radial-gradient(circle at 85% 35%, rgba(255, 0, 128, 0.6) 1px, transparent 1px),
    radial-gradient(circle at 45% 75%, rgba(138, 43, 226, 0.4) 1px, transparent 1px),
    radial-gradient(circle at 70% 15%, rgba(0, 217, 255, 0.3) 0.5px, transparent 0.5px);
  background-size: 150px 150px, 120px 120px, 80px 80px, 200px 200px;
  animation: quantum-drift 80s linear infinite;
  pointer-events: none;
}

@keyframes quantum-drift {
  0% { transform: translate(0, 0) rotate(0deg) scale(1); }
  25% { transform: translate(-10px, -15px) rotate(90deg) scale(1.1); }
  50% { transform: translate(-25px, -10px) rotate(180deg) scale(0.9); }
  75% { transform: translate(-15px, -25px) rotate(270deg) scale(1.05); }
  100% { transform: translate(0, 0) rotate(360deg) scale(1); }
}

/* 超微细网格线 */
.page-bg::after {
  content: '';
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(0, 217, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 217, 255, 0.02) 1px, transparent 1px);
  background-size: 40px 40px;
  animation: grid-pulse 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes grid-pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.02); }
}

/* 基础容器 - 响应式优化 */
.container {  
  min-height: 100vh;  
  display: flex;  
  flex-direction: column;  
  align-items: center;  
  justify-content: flex-start;  
  padding: calc(env(safe-area-inset-top) + var(--spacing-2xl)) clamp(32rpx, 8vw, 56rpx) calc(env(safe-area-inset-bottom) + 150rpx);
  box-sizing: border-box;  
  position: relative;  
  z-index: 1;
  max-width: 750rpx;
  margin: 0 auto;
}

/* 霓虹玻璃卡片 - 升级版 */
.glass-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  backdrop-filter: blur(var(--blur));
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  width: 100%;
  position: relative;
  overflow: hidden;
  transition: all var(--transition-smooth);
  box-shadow: var(--shadow-neon-sm);
}

.glass-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, 
    rgba(0, 217, 255, 0.05) 0%, 
    transparent 30%, 
    rgba(255, 0, 128, 0.03) 70%, 
    transparent 100%);
  border-radius: var(--border-radius);
  opacity: 0;
  transition: opacity var(--transition-smooth);
  pointer-events: none;
}

.glass-card:hover {
  background: var(--glass-hover);
  border-color: rgba(0, 217, 255, 0.3);
  transform: translateY(-4rpx) scale(1.01);
  box-shadow: var(--shadow-neon-md);
}

.glass-card:hover::before {
  opacity: 1;
}

.glass-card:active {
  background: var(--glass-active);
  transform: translateY(-2rpx) scale(0.99);
  box-shadow: var(--shadow-pink-sm);
}

/* 超级输入框 */
.glass-input {
  width: 100%;
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  border-radius: var(--border-radius-input);
  padding: var(--spacing-lg);
  color: var(--text-primary);
  font-size: 32rpx;
  font-weight: 300;
  letter-spacing: 1rpx;
  transition: all var(--transition-smooth);
  backdrop-filter: blur(var(--blur));
  box-sizing: border-box;
  position: relative;
}

.glass-input::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, 
    var(--primary-500), 
    var(--accent-500), 
    var(--secondary-500));
  border-radius: var(--border-radius-input);
  opacity: 0;
  transition: opacity var(--transition-smooth);
  z-index: -1;
  filter: blur(8rpx);
}

.glass-input:focus {
  border-color: var(--primary-500);
  background: var(--glass-focus);
  outline: none;
  box-shadow: var(--shadow-neon-md);
  transform: scale(1.02);
}

.glass-input:focus::before {
  opacity: 0.6;
}

.glass-input::placeholder {
  color: var(--text-tertiary);
  font-style: italic;
}

/* 量子按钮 */
.btn-primary {
  width: 100%;
  background: linear-gradient(135deg, 
    var(--primary-500) 0%, 
    var(--secondary-500) 50%, 
    var(--accent-500) 100%);
  border: none;
  border-radius: var(--border-radius-btn);
  color: var(--text-primary);
  font-weight: 500;
  font-size: 34rpx;
  line-height: 96rpx;
  text-align: center;
  transition: all var(--transition-bounce);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-neon-md);
  cursor: pointer;
  letter-spacing: 2rpx;
  text-transform: uppercase;
}

.btn-primary::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    transparent 50%, 
    rgba(255, 255, 255, 0.1) 100%);
  transform: translateX(-100%);
  transition: transform 0.8s ease;
}

.btn-primary:hover::before {
  transform: translateX(100%);
}

.btn-primary:active {
  transform: scale(0.95);
  box-shadow: var(--shadow-neon-sm);
}

.btn-primary:disabled {
  background: var(--surface-700);
  color: var(--text-muted);
  box-shadow: none;
  cursor: not-allowed;
}

/* 浮动动画 */
@keyframes float-gentle {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-10rpx) rotate(1deg); }
}

.floating {
  animation: float-gentle 4s ease-in-out infinite;
}

/* 渐显动画 */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(40rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fade-in-up 0.8s var(--transition-smooth) forwards;
}

/* 脉冲发光 */
@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: var(--shadow-neon-sm);
    opacity: 1; 
  }
  50% { 
    box-shadow: var(--shadow-neon-lg);
    opacity: 0.9; 
  }
}

.pulse {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 深夜赛博背景 */
.page-bg {
  position: fixed;
  inset: 0;
  background:
    radial-gradient(ellipse at 30% 10%, rgba(255, 0, 120, 0.30) 0%, transparent 60%),
    radial-gradient(ellipse at 80% 80%, rgba(0, 180, 255, 0.25) 0%, transparent 55%),
    radial-gradient(ellipse at 50% 50%, rgba(108, 93, 211, 0.15) 0%, transparent 70%),
    var(--bg-900);
  z-index: -10;
}

/* 星尘粒子背景 */
.page-bg::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(23, 224, 255, 0.1) 1px, transparent 1px),
    radial-gradient(circle at 80% 30%, rgba(108, 93, 211, 0.08) 1px, transparent 1px),
    radial-gradient(circle at 40% 70%, rgba(255, 255, 255, 0.06) 1px, transparent 1px),
    radial-gradient(circle at 90% 90%, rgba(23, 224, 255, 0.12) 1px, transparent 1px);
  background-size: 120px 120px, 80px 80px, 60px 60px, 100px 100px;
  animation: star-drift 60s linear infinite;
  pointer-events: none;
}

@keyframes star-drift {
  0% { transform: translate(0, 0) rotate(0deg); }
  100% { transform: translate(-20px, -20px) rotate(360deg); }
}

/* 微动态网格 */
.page-bg::after {
  content: '';
  position: absolute;
  inset: 0;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60"><defs><pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse"><path d="M 60 0 L 0 0 0 60" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="60" height="60" fill="url(%23grid)"/></svg>');
  animation: grid-flow 40s linear infinite;
  pointer-events: none;
}

@keyframes grid-flow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

/* 玻璃拟态卡片 */
.glass-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  backdrop-filter: blur(var(--blur));
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  width: 100%;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: var(--shadow-sm);
}

.glass-card:hover {
  background: var(--glass-hover);
  border-color: rgba(255, 255, 255, 0.12);
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.glass-card:active {
  background: var(--glass-active);
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 输入框样式 */
.glass-input {
  width: 100%;
  background: var(--glass-hover);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-input);
  padding: var(--spacing-md);
  color: var(--text-primary);
  font-size: 28rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(var(--blur));
  box-sizing: border-box;
}

.glass-input:focus {
  border-color: var(--accent-500);
  background: rgba(23, 224, 255, 0.08);
  outline: none;
  box-shadow: 0 0 0 2rpx rgba(23, 224, 255, 0.2);
}

.glass-input::placeholder {
  color: var(--text-tertiary);
}

/* 主按钮 */
.btn-primary {
  width: 100%;
  background: linear-gradient(135deg, var(--accent-500) 0%, var(--primary-500) 100%);
  border: none;
  border-radius: var(--border-radius-btn);
  color: var(--text-primary);
  font-weight: 600;
  font-size: 32rpx;
  line-height: 88rpx;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  cursor: pointer;
}

.btn-primary:active {
  transform: scale(0.97);
  opacity: 0.9;
  box-shadow: var(--shadow-sm);
}

.btn-primary:disabled {
  background: var(--surface-700);
  color: var(--text-muted);
  cursor: not-allowed;
}

/* 次要按钮 */
.btn-secondary {
  width: 100%;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-btn);
  color: var(--text-primary);
  font-weight: 500;
  font-size: 28rpx;
  line-height: 76rpx;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  backdrop-filter: blur(var(--blur));
  box-shadow: var(--shadow-sm);
}

.btn-secondary:active {
  transform: scale(0.97);
  background: var(--glass-hover);
}

/* 文字层级 */
.text-hero {
  font-size: 64rpx;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.text-title {
  font-size: 48rpx;
  font-weight: 600;
  line-height: 1.3;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.text-subtitle {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 1.4;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.text-body {
  font-size: 28rpx;
  font-weight: 400;
  line-height: 1.5;
  color: var(--text-primary);
}

.text-caption {
  font-size: 24rpx;
  font-weight: 400;
  line-height: 1.4;
  color: var(--text-tertiary);
}

.text-small {
  font-size: 20rpx;
  font-weight: 400;
  line-height: 1.3;
  color: var(--text-muted);
}

/* 品牌渐变文字 */
.text-gradient {
  background: linear-gradient(135deg, var(--accent-500) 0%, var(--primary-500) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 页面进入动画 */
.page-enter {
  animation: page-fade-in 0.6s cubic-bezier(0.23, 1, 0.32, 1) forwards;
}

@keyframes page-fade-in {
  0% {
    opacity: 0;
    transform: translateY(24rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片进入动画 */
.card-enter {
  animation: card-slide-up 0.4s cubic-bezier(0.23, 1, 0.32, 1) forwards;
}

@keyframes card-slide-up {
  0% {
    opacity: 0;
    transform: translateY(12rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 设备类型适配 */
.device-small {
  --adaptive-font-scale: 0.9;
  --adaptive-spacing-scale: 0.8;
  --adaptive-component-scale: 0.9;
}

.device-medium {
  --adaptive-font-scale: 1.0;
  --adaptive-spacing-scale: 1.0;
  --adaptive-component-scale: 1.0;
}

.device-large {
  --adaptive-font-scale: 1.05;
  --adaptive-spacing-scale: 1.1;
  --adaptive-component-scale: 1.05;
}

.device-tablet {
  --adaptive-font-scale: 1.1;
  --adaptive-spacing-scale: 1.2;
  --adaptive-component-scale: 1.1;
}

/* 刘海屏适配 */
.device-notch {
  --safe-area-top-extra: 20rpx;
  --safe-area-bottom-extra: 20rpx;
}

/* 平台特定适配 */
.platform-android {
  --platform-font-weight-adjust: 100;
}

.platform-ios {
  --platform-font-weight-adjust: 0;
}

/* 响应式断点 - 增强版 */
@media screen and (max-width: 320px) {
  .container {
    padding: calc(env(safe-area-inset-top) + var(--spacing-lg)) clamp(20rpx, 4vw, 32rpx) calc(env(safe-area-inset-bottom) + 130rpx);
    max-width: 100vw;
  }

  .glass-card {
    padding: clamp(24rpx, 5vw, 32rpx);
    margin-bottom: clamp(16rpx, 3vw, 24rpx);
  }

  .text-hero {
    font-size: clamp(40rpx, 8vw, 48rpx);
    line-height: 1.2;
  }

  .text-title {
    font-size: clamp(32rpx, 6vw, 40rpx);
    line-height: 1.3;
  }
}

@media screen and (max-width: 375px) {
  .container {
    padding: calc(env(safe-area-inset-top) + var(--spacing-xl)) clamp(24rpx, 5vw, 40rpx) calc(env(safe-area-inset-bottom) + 140rpx);
    max-width: 100vw;
  }

  .glass-card {
    padding: clamp(28rpx, 5.5vw, 36rpx);
  }

  .text-hero {
    font-size: clamp(48rpx, 9vw, 56rpx);
  }

  .text-title {
    font-size: clamp(36rpx, 7vw, 44rpx);
  }
}

@media screen and (min-width: 414px) {
  .container {
    padding: calc(env(safe-area-inset-top) + var(--spacing-2xl)) clamp(32rpx, 6vw, 64rpx) calc(env(safe-area-inset-bottom) + 160rpx);
    max-width: 750rpx;
    margin: 0 auto;
  }

  .glass-card {
    padding: clamp(36rpx, 6.5vw, 48rpx);
    max-width: 650rpx;
    margin-left: auto;
    margin-right: auto;
  }

  .text-hero {
    font-size: clamp(56rpx, 10vw, 72rpx);
  }

  .text-title {
    font-size: clamp(44rpx, 8vw, 56rpx);
  }
}

@media screen and (min-width: 500px) {
  .container {
    padding: calc(env(safe-area-inset-top) + 80rpx) clamp(40rpx, 8vw, 80rpx) calc(env(safe-area-inset-bottom) + 180rpx);
    max-width: 800rpx;
    margin: 0 auto;
  }

  .glass-card {
    padding: clamp(48rpx, 8vw, 80rpx);
    max-width: 700rpx;
    margin-left: auto;
    margin-right: auto;
  }

  .text-hero {
    font-size: clamp(64rpx, 12vw, 96rpx);
  }

  .text-title {
    font-size: clamp(48rpx, 10vw, 72rpx);
  }
}

/* 横屏适配 - 增强版 */
@media screen and (orientation: landscape) {
  .container {
    padding: calc(env(safe-area-inset-top) + 20rpx) clamp(32rpx, 8vw, 64rpx) calc(env(safe-area-inset-bottom) + 60rpx);
    flex-direction: column;
    justify-content: flex-start;
    max-width: 90vw;
    margin: 0 auto;
  }

  .glass-card {
    width: 100%;
    max-width: none;
    margin-bottom: clamp(16rpx, 3vw, 24rpx);
    padding: clamp(24rpx, 4vw, 40rpx);
  }

  .text-hero {
    font-size: clamp(36rpx, 6vw, 48rpx);
    margin-bottom: clamp(16rpx, 3vw, 24rpx);
  }

  .text-title {
    font-size: clamp(32rpx, 5vw, 40rpx);
    margin-bottom: clamp(12rpx, 2vw, 20rpx);
  }

  /* 横屏时调整按钮和输入框 */
  .btn-primary {
    line-height: clamp(60rpx, 12vw, 80rpx);
    font-size: clamp(28rpx, 5vw, 36rpx);
  }

  .glass-input {
    padding: clamp(16rpx, 3vw, 24rpx);
    font-size: clamp(24rpx, 4vw, 32rpx);
  }
}

/* 响应式容器 - 增强版 */
.container {  
  min-height: 100vh;  
  display: flex;  
  flex-direction: column;  
  align-items: center;  
  justify-content: flex-start;  
  padding: calc(env(safe-area-inset-top) + 40rpx) 5vw calc(env(safe-area-inset-bottom) + 150rpx) 5vw;  
  box-sizing: border-box;  
  position: relative;  
  z-index: 1;  
  transform-style: preserve-3d;  
  transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 超小屏幕适配 (小于320px宽度) */
@media screen and (max-width: 320px) {
  .container {
    padding: calc(env(safe-area-inset-top) + 30rpx) 2vw calc(env(safe-area-inset-bottom) + 130rpx) 2vw;
  }
  
  .title {
    margin-top: 20rpx !important;
    margin-bottom: 30rpx !important;
    font-size: clamp(36rpx, 7vw, 48rpx) !important;
    letter-spacing: 2rpx !important;
  }
  
  .futuristic-card {
    padding: clamp(20rpx, 4vw, 30rpx) !important;
    margin-bottom: 20rpx !important;
  }
}

/* 小屏幕适配 (320px-375px) */
@media screen and (max-width: 375px) {  
  .container {    
    padding: calc(env(safe-area-inset-top) + 35rpx) 3vw calc(env(safe-area-inset-bottom) + 140rpx) 3vw;  
  }
  
  .title {
    margin-top: 25rpx !important;
    margin-bottom: 35rpx !important;
    font-size: clamp(40rpx, 8vw, 52rpx) !important;
    letter-spacing: 3rpx !important;
  }
  
  .futuristic-card {
    padding: clamp(25rpx, 4.5vw, 35rpx) !important;
    margin-bottom: 25rpx !important;
  }
}

/* 中等屏幕适配 (375px-414px) */
@media screen and (min-width: 375px) and (max-width: 414px) {
  .container {
    padding: calc(env(safe-area-inset-top) + 50rpx) 4vw calc(env(safe-area-inset-bottom) + 150rpx) 4vw;
  }
  
  .title {
    font-size: clamp(48rpx, 9vw, 60rpx) !important;
  }
}

/* 大屏幕适配 (414px以上) */
@media screen and (min-width: 414px) {
  .container {
    padding: calc(env(safe-area-inset-top) + 60rpx) 6vw calc(env(safe-area-inset-bottom) + 160rpx) 6vw;
    max-width: 750rpx;
    margin: 0 auto;
  }
  
  .title {
    font-size: clamp(52rpx, 10vw, 68rpx) !important;
  }
  
  .futuristic-card {
    max-width: 650rpx !important;
  }
}

/* 超大屏幕适配 (大于500px宽度，如平板) */
@media screen and (min-width: 500px) {
  .container {
    padding: calc(env(safe-area-inset-top) + 80rpx) 8vw calc(env(safe-area-inset-bottom) + 180rpx) 8vw;
    max-width: 800rpx;
  }
  
  .title {
    font-size: clamp(60rpx, 12vw, 80rpx) !important;
    margin: 40rpx 0 50rpx 0 !important;
  }
  
  .futuristic-card {
    max-width: 700rpx !important;
    padding: clamp(40rpx, 6vw, 60rpx) !important;
  }
}

/* 针对特定设备的适配 */
/* iPhone SE (1st generation) */
@media screen and (device-width: 320px) and (device-height: 568px) {
  .container {
    padding-top: calc(env(safe-area-inset-top) + 30rpx);
    padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);
  }
}

/* iPhone X/XS 系列 */
@media screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) {
  .container {
    padding-top: calc(env(safe-area-inset-top) + 60rpx);
    padding-bottom: calc(env(safe-area-inset-bottom) + 80rpx);
  }
  
  .title {
    margin-top: 30rpx !important;
  }
}

/* iPhone XR/11 */
@media screen and (device-width: 414px) and (device-height: 896px) {
  .container {
    padding-top: calc(env(safe-area-inset-top) + 70rpx);
    padding-bottom: calc(env(safe-area-inset-bottom) + 90rpx);
  }
}

/* iPhone 12/13/14 Pro Max */
@media screen and (device-width: 428px) and (device-height: 926px) {
  .container {
    padding-top: calc(env(safe-area-inset-top) + 80rpx);
    padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
  .container {
    padding: calc(env(safe-area-inset-top) + 20rpx) 8vw calc(env(safe-area-inset-bottom) + 40rpx) 8vw;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .title {
    width: 100%;
    margin: 20rpx 0 30rpx 0 !important;
    font-size: clamp(36rpx, 6vw, 48rpx) !important;
  }
  
  .futuristic-card {
    width: 100%;
    max-width: none !important;
    margin-bottom: 20rpx !important;
  }
}

/* 页面切换3D动画 */
.page-enter {
  animation: page-enter 0.8s cubic-bezier(0.23, 1, 0.32, 1);
  transform-origin: center;
}

@keyframes page-enter {
  0% {
    opacity: 0;
    transform: translateY(30px) rotateX(10deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) rotateX(0);
  }
}

/* 量子时空背景效果 */
.futuristic-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(ellipse at top, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at bottom, rgba(157, 78, 221, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #000000 0%, #1a0033 25%, #2d1b69 50%, #1a0033 75%, #000000 100%);
  z-index: -2;
  overflow: hidden;
  animation: quantum-shift 20s infinite alternate;
}

/* 量子背景动画 */
@keyframes quantum-shift {
  0% {
    background-position: 0% 0%;
    filter: hue-rotate(0deg);
  }
  50% {
    background-position: 100% 50%;
    filter: hue-rotate(60deg);
  }
  100% {
    background-position: 0% 100%;
    filter: hue-rotate(120deg);
  }
}

.futuristic-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 30%, rgba(0, 255, 255, 0.2) 0%, transparent 25%),
    radial-gradient(circle at 80% 70%, rgba(255, 20, 147, 0.2) 0%, transparent 25%),
    radial-gradient(circle at 50% 20%, rgba(157, 78, 221, 0.15) 0%, transparent 40%),
    radial-gradient(circle at 30% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 30%);
  z-index: -1;
  background-size: 300% 300%;
  animation: cosmic-pulse 15s infinite alternate;
}

@keyframes cosmic-pulse {
  0% { 
    opacity: 0.6; 
    background-position: 0% 0%;
  }
  50% { 
    opacity: 1; 
    background-position: 100% 100%;
  }
  100% { 
    opacity: 0.8; 
    background-position: 0% 100%;
  }
}

.futuristic-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  background-size: 60px 60px;
  opacity: 0.4;
  z-index: -1;
  animation: grid-flow 25s linear infinite;
}

@keyframes grid-flow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

/* 量子粒子效果 */
.floating-particles {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  pointer-events: none;
}

.particle {
  position: absolute;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.8) 0%, rgba(157, 78, 221, 0.4) 50%, transparent 100%);
  border-radius: 50%;
  width: 6rpx;
  height: 6rpx;
  animation: quantum-float 20s infinite linear;
  box-shadow: 0 0 20rpx rgba(0, 255, 255, 0.6);
}

@keyframes quantum-float {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(0.5);
    opacity: 0;
  }
  20% {
    opacity: 1;
    transform: translate(50rpx, -100rpx) rotate(90deg) scale(1);
  }
  80% {
    opacity: 0.8;
    transform: translate(250rpx, -600rpx) rotate(270deg) scale(1.2);
  }
  100% {
    transform: translate(400rpx, -800rpx) rotate(360deg) scale(0.3);
    opacity: 0;
  }
}

/* 时空线条动画 */
.tech-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.tech-line {
  position: absolute;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(0, 255, 255, 0.3), 
    rgba(255, 20, 147, 0.3), 
    rgba(157, 78, 221, 0.3), 
    transparent);
  height: 2rpx;
  width: 100%;
  animation: time-flow 30s infinite linear;
  box-shadow: 0 0 10rpx rgba(0, 255, 255, 0.5);
}

@keyframes time-flow {
  0% { 
    transform: translateX(-100%) scaleX(0.5); 
    opacity: 0;
  }
  20% {
    opacity: 1;
    transform: translateX(0) scaleX(1);
  }
  80% {
    opacity: 0.8;
    transform: translateX(50%) scaleX(1.2);
  }
  100% { 
    transform: translateX(200%) scaleX(0.3); 
    opacity: 0;
  }
}

/* 响应式3D卡片效果 - 时空版 */
.futuristic-card {
  background: 
    linear-gradient(135deg, 
      rgba(0, 20, 40, 0.9) 0%, 
      rgba(26, 0, 51, 0.85) 25%,
      rgba(45, 27, 105, 0.8) 50%,
      rgba(26, 0, 51, 0.85) 75%,
      rgba(0, 20, 40, 0.9) 100%);
  border-radius: 20rpx;
  border: 2rpx solid;
  border-image: linear-gradient(45deg, 
    rgba(0, 255, 255, 0.6), 
    rgba(157, 78, 221, 0.6), 
    rgba(255, 20, 147, 0.6),
    rgba(0, 255, 255, 0.6)) 1;
  box-shadow: 
    0 8rpx 40rpx rgba(0, 255, 255, 0.2),
    inset 0 2rpx 10rpx rgba(0, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  padding: clamp(30rpx, 5vw, 50rpx);
  margin-bottom: 30rpx;
  width: 90%;
  max-width: 600rpx;
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
  transition: all 0.5s ease;
}

.futuristic-card::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(45deg, 
    rgba(0, 255, 255, 0.6), 
    rgba(157, 78, 221, 0.6), 
    rgba(255, 20, 147, 0.6),
    rgba(0, 255, 255, 0.6));
  border-radius: 20rpx;
  z-index: -1;
  animation: border-glow 3s ease-in-out infinite alternate;
}

@keyframes border-glow {
  0% {
    opacity: 0.6;
    filter: blur(2rpx);
  }
  100% {
    opacity: 1;
    filter: blur(6rpx);
  }
}

.futuristic-card:hover {
  transform: translateY(-10rpx) rotateX(5deg);
  box-shadow: 
    0 20rpx 60rpx rgba(0, 255, 255, 0.3),
    inset 0 4rpx 20rpx rgba(0, 255, 255, 0.15);
}

/* 霓虹文字效果 */
.neon-text {
  color: #00FFFF;
  text-shadow: 
    0 0 10rpx rgba(0, 255, 255, 0.8),
    0 0 20rpx rgba(0, 255, 255, 0.6),
    0 0 40rpx rgba(0, 255, 255, 0.4),
    0 0 80rpx rgba(0, 255, 255, 0.2);
  animation: neon-flicker 2s ease-in-out infinite alternate;
}

@keyframes neon-flicker {
  0%, 100% {
    text-shadow: 
      0 0 10rpx rgba(0, 255, 255, 0.8),
      0 0 20rpx rgba(0, 255, 255, 0.6),
      0 0 40rpx rgba(0, 255, 255, 0.4);
  }
  50% {
    text-shadow: 
      0 0 5rpx rgba(0, 255, 255, 1),
      0 0 15rpx rgba(0, 255, 255, 0.8),
      0 0 30rpx rgba(0, 255, 255, 0.6),
      0 0 60rpx rgba(0, 255, 255, 0.4);
  }
}

/* 霓虹边框效果 */
.neon-border {
  border: 2rpx solid rgba(0, 255, 255, 0.6);
  box-shadow: 
    0 0 10rpx rgba(0, 255, 255, 0.4),
    inset 0 0 10rpx rgba(0, 255, 255, 0.2);
  animation: border-pulse 2s ease-in-out infinite;
}

@keyframes border-pulse {
  0%, 100% {
    border-color: rgba(0, 255, 255, 0.6);
    box-shadow: 
      0 0 10rpx rgba(0, 255, 255, 0.4),
      inset 0 0 10rpx rgba(0, 255, 255, 0.2);
  }
  50% {
    border-color: rgba(0, 255, 255, 1);
    box-shadow: 
      0 0 20rpx rgba(0, 255, 255, 0.8),
      inset 0 0 20rpx rgba(0, 255, 255, 0.4);
  }
}

/* 响应式按钮样式 */
.btn-adjust {
  width: clamp(60rpx, 12vw, 80rpx);
  height: clamp(60rpx, 12vw, 80rpx);
  border-radius: 50%;
  font-size: clamp(36rpx, 8vw, 50rpx);
  line-height: 1;
  text-align: center;
  background: rgba(16, 30, 70, 0.9);
  border: 1px solid rgba(87, 127, 255, 0.6);
  color: #5d9cff;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  font-weight: bold;
}

.btn-adjust:active {
  transform: scale(0.9) translateY(5rpx);
  box-shadow: 0 0 15rpx rgba(93, 156, 255, 0.8);
}

/* 底部导航栏样式增强 */
.tab-bar {
  background: rgba(0, 51, 102, 0.95) !important;
  backdrop-filter: blur(20rpx) !important;
  border-top: 1px solid rgba(93, 156, 255, 0.3) !important;
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.3) !important;
}

/* 底部导航栏文字样式 */
.tab-bar .tab-bar-item {
  font-size: 38rpx !important;
  font-weight: bold !important;
  font-family: -apple-system-font, Helvetica Neue, Helvetica, sans-serif !important;
  text-shadow: 0 0 8rpx rgba(93, 156, 255, 0.5) !important;
}

.tab-bar .tab-bar-item.tab-bar-item-active {
  color: #5d9cff !important;
  font-weight: 900 !important;
  text-shadow: 0 0 15rpx rgba(93, 156, 255, 0.8) !important;
  font-size: 42rpx !important;
}

/* 页面切换3D动画 */
.page-transition {
  animation-duration: 0.5s;
  animation-fill-mode: both;
}

.slide-in {
  animation-name: slideInRight;
}

.slide-out {
  animation-name: slideOutLeft;
}

@keyframes slideInRight {
  from {
    transform: translate3d(100%, 0, 0) rotate3d(0, 1, 0, 10deg);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0) rotate3d(0, 0, 0, 0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  to {
    transform: translate3d(-30%, 0, 0) rotate3d(0, 1, 0, -10deg);
    opacity: 0;
  }
}

/* 光效跟随效果 */
.glow-follower {
  position: absolute;
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(93, 156, 255, 0.4) 0%, rgba(93, 156, 255, 0.1) 50%, rgba(93, 156, 255, 0) 70%);
  pointer-events: none;
  transform: translate(-50%, -50%);
  z-index: 1;
  filter: blur(10rpx);
  transition: opacity 0.5s ease;
}

/* 按钮按下状态 */
.button-pressed {
  transform: scale(0.95) translateY(5rpx) !important;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.3) !important;
}

/* 按钮发光效果 */
.btn-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  border-radius: 45rpx;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.calculate-btn:active .btn-glow {
  opacity: 0.3;
  animation: btn-glow-pulse 0.5s ease;
}

@keyframes btn-glow-pulse {
  0% { opacity: 0; }
  50% { opacity: 0.5; }
  100% { opacity: 0; }
}

/* 页面退出动画 */
.page-exit {
  animation: page-exit 0.5s cubic-bezier(0.23, 1, 0.32, 1) forwards;
}

@keyframes page-exit {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
}

/* 底部tabBar上移1厘米(75rpx) */page {  --safe-area-inset-bottom: 75rpx !important;  padding-bottom: calc(env(safe-area-inset-bottom) + 75rpx) !important;}/* 尝试通过多种方式让tabBar上移 */.wx-bottom-tab-bar,.weui-tabbar,tabbar,[class*="tabbar"] {  transform: translateY(-75rpx) !important;  margin-bottom: 75rpx !important;  position: relative !important;  z-index: 1000 !important;}/* 强制设置tabBar位置 */page > tabbar,page > .wx-bottom-tab-bar {  bottom: 75rpx !important;  position: fixed !important;}/* 增大tabBar字体并保持样式 */.wx-tabbar .wx-tabbar-item,.weui-tabbar__item,.tab-bar .tab-bar-item {  font-size: 32rpx !important;  font-weight: 600 !important;  line-height: 1.3 !important;}.wx-tabbar .wx-tabbar-item.wx-tabbar-item-active,.weui-tabbar__item.weui-bar__item_on,.tab-bar .tab-bar-item.tab-bar-item-active {  font-size: 36rpx !important;  font-weight: 700 !important;}/* tabBar文字标签增大 */.wx-tabbar .wx-tabbar-label,.weui-tabbar__label {  font-size: 32rpx !important;  font-weight: 600 !important;}.wx-tabbar .wx-tabbar-item-active .wx-tabbar-label,.weui-tabbar__item.weui-bar__item_on .weui-tabbar__label {  font-size: 36rpx !important;  font-weight: 700 !important;}/* 确保页面内容不被遮挡 */.container {  padding-bottom: calc(env(safe-area-inset-bottom) + 150rpx) !important;}/* 强制覆盖原生tabBar样式 */wx-tabbar {  font-size: 32rpx !important;  position: fixed !important;  bottom: 75rpx !important;  left: 0 !important;  right: 0 !important;  z-index: 9999 !important;}wx-tabbar wx-tabbar-item {  font-size: 32rpx !important;  font-weight: 600 !important;}wx-tabbar wx-tabbar-item[active] {  font-size: 36rpx !important;  font-weight: 700 !important;} 