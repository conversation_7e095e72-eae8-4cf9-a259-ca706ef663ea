Page({
  data: {
    targetDays: 30000,
    targetYears: '82.2',
    minTargetDays: 25000,
    maxTargetDays: 40000,
    step: 100, // 减小步长以便更精确控制
    targetDate: '', // 目标日期
    minDate: '', // 最小可选日期
    remainingDays: 0, // 剩余天数
    remainingYears: '', // 剩余年数
    targetDateFromDays: '', // 从天数计算出的目标日期
    birthDate: '', // 出生日期
    isAdjusting: false // 防抖标志
  },

  onLoad: function (options) {
    // 读取缓存中的设置
    const targetDays = wx.getStorageSync('targetDays') || 30000;
    const birthDate = wx.getStorageSync('birthDate') || '';
    const targetDate = wx.getStorageSync('targetDate') || '';
    
    this.setData({
      birthDate: birthDate
    });
    
    this.initDateRange();
    
    if (targetDate) {
      this.setData({
        targetDate: targetDate
      });
      this.calculateFromDate();
    } else {
      this.updateTargetData(targetDays);
    }
  },

  onShow: function () {
    // 更新tabBar状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateSelected();
    }
  },

  // 初始化日期范围
  initDateRange: function() {
    const now = new Date();
    const currentYear = now.getFullYear();
    
    // 设置日期选择范围：从当前年份开始，无上限
    this.setData({
      minDate: `${currentYear}-01-01`
    });
  },

  // 日期选择变化
  onDateChange: function(e) {
    const selectedDate = e.detail.value;
    this.setData({
      targetDate: selectedDate
    });
    
    this.calculateFromDate();
    this.saveTargetData();
  },

  // 从选择的日期计算剩余天数
  calculateFromDate: function() {
    if (!this.data.targetDate) return;
    
    const now = new Date();
    const target = new Date(this.data.targetDate);
    const diffTime = target - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 0) {
      this.setData({
        remainingDays: diffDays,
        remainingYears: Math.round(diffDays / 365),
        targetDays: diffDays,
        targetYears: Math.round(diffDays / 365)
      });
    }
  },
  
  // 减少目标天数
  decreaseTargetDays: function() {
    if (this.data.isAdjusting) return; // 防抖
    
    this.setData({ isAdjusting: true });
    
    let currentDays = this.data.targetDays;
    currentDays = Math.max(this.data.minTargetDays, currentDays - this.data.step);
    this.updateTargetData(currentDays);
    this.calculateDateFromDays(currentDays);
    this.saveTargetData();
    
    // 释放防抖
    setTimeout(() => {
      this.setData({ isAdjusting: false });
    }, 200);
  },
  
  // 增加目标天数
  increaseTargetDays: function() {
    if (this.data.isAdjusting) return; // 防抖
    
    this.setData({ isAdjusting: true });
    
    let currentDays = this.data.targetDays;
    currentDays = Math.min(this.data.maxTargetDays, currentDays + this.data.step);
    this.updateTargetData(currentDays);
    this.calculateDateFromDays(currentDays);
    this.saveTargetData();
    
    // 释放防抖
    setTimeout(() => {
      this.setData({ isAdjusting: false });
    }, 200);
  },
  
  // 从天数计算目标日期
  calculateDateFromDays: function(days, isSliding = false) {
    const birthDate = this.data.birthDate;
    let baseDate;
    
    if (birthDate) {
      // 如果有出生日期，从出生日期开始计算
      baseDate = new Date(birthDate);
    } else {
      // 如果没有出生日期，从当前日期开始计算
      baseDate = new Date();
    }
    
    const targetDate = new Date(baseDate.getTime() + days * 24 * 60 * 60 * 1000);
    const dateString = targetDate.toISOString().split('T')[0];
    
    if (isSliding) {
      return dateString;
    } else {
      this.setData({
        targetDateFromDays: dateString,
        targetDate: dateString
      });
    }
    
    return dateString;
  },
  
  // 更新数据方法
  updateTargetData: function(targetDays) {
    // 计算岁数，保留一位小数
    const targetYears = (targetDays / 365).toFixed(1);
    // 计算目标日期
    const birthDate = this.data.birthDate;
    let baseDate;
    if (birthDate) {
      baseDate = new Date(birthDate);
    } else {
      baseDate = new Date();
    }
    const targetDateObj = new Date(baseDate.getTime() + targetDays * 24 * 60 * 60 * 1000);
    const dateString = targetDateObj.toISOString().split('T')[0];
    this.setData({
      targetDays: targetDays,
      targetYears: targetYears,
      targetDateFromDays: dateString,
      targetDate: dateString
    });
  },
  
  // 保存数据到缓存
  saveTargetData: function() {
    wx.setStorageSync('targetDays', this.data.targetDays);
    wx.setStorageSync('targetDate', this.data.targetDate);
    
    wx.showToast({
      title: '设置已保存',
      icon: 'success',
      duration: 1000
    });
  },
  
  // 返回首页
  backToHome: function() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
}) 