// pages/wishes/wishes.js
const deviceUtils = require('../../utils/deviceUtils.js');

Page({
  data: {
    wishes: [],
    newWish: '',
    newWishDate: '', // 新愿望的目标日期
    today: '', // 今天的日期
    maxDate: '', // 最大可选日期
    urgentWishes: [], // 紧急愿望（30天内）
    shortTermWishes: [], // 短期愿望（1年内）
    longTermWishes: [], // 长期愿望（1年以上）
    noDateWishes: [] // 无日期愿望
  },
  
  onLoad: function (options) {
    // 获取设备适配信息
    const app = getApp();
    const deviceClassName = app.globalData.deviceClassName || '';
    const adaptiveParams = app.globalData.adaptiveParams || {};

    this.setData({
      deviceClassName: deviceClassName,
      adaptiveParams: adaptiveParams
    });

    this.initDates();
    this.loadWishes();
  },
  
  onShow: function () {
    this.loadWishes();
    
    // 更新tabBar状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateSelected();
    }
  },

  // 初始化日期范围
  initDates: function() {
    const today = new Date();
    const maxDate = new Date();
    maxDate.setFullYear(today.getFullYear() + 50); // 最大50年后
    
    this.setData({
      today: today.toISOString().split('T')[0],
      maxDate: maxDate.toISOString().split('T')[0]
    });
  },

  // 目标日期选择
  onWishDateChange: function(e) {
    this.setData({
      newWishDate: e.detail.value
    });
  },

  // 清除日期
  clearDate: function() {
    this.setData({
      newWishDate: ''
    });
  },
  
  // 从本地缓存加载愿望列表
  loadWishes: function() {
    const wishes = wx.getStorageSync('wishes') || [];
    // 计算每个愿望的剩余天数并添加详细信息
    const processedWishes = wishes.map(wish => {
      const processed = { ...wish };
      
      if (wish.targetDate) {
        const target = new Date(wish.targetDate);
        const now = new Date();
        const diffTime = target - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        processed.daysLeft = diffDays;
        
        // 生成详细信息
        if (diffDays > 0) {
          processed.detailInfo = `距离目标还有 ${diffDays} 天，约 ${(diffDays / 365).toFixed(1)} 年。加油实现这个愿望！`;
        } else if (diffDays === 0) {
          processed.detailInfo = '就是今天！是时候实现这个愿望了！';
        } else {
          processed.detailInfo = `目标日期已过去 ${Math.abs(diffDays)} 天，但永远不晚，继续努力！`;
        }
      } else {
        processed.detailInfo = '这是一个没有时限的愿望，慢慢来，总有一天会实现的。';
      }
      
      return processed;
    });

    // 按类别分组
    this.categorizeWishes(processedWishes);
    
    this.setData({
      wishes: processedWishes
    });
  },

  // 按时间分类愿望
  categorizeWishes: function(wishes) {
    const urgent = [];
    const shortTerm = [];
    const longTerm = [];
    const noDate = [];

    wishes.forEach(wish => {
      if (!wish.targetDate) {
        noDate.push(wish);
      } else if (wish.daysLeft <= 30 && wish.daysLeft >= 0) {
        urgent.push(wish);
      } else if (wish.daysLeft <= 365 && wish.daysLeft > 30) {
        shortTerm.push(wish);
      } else {
        longTerm.push(wish);
      }
    });

    // 按剩余天数排序
    urgent.sort((a, b) => a.daysLeft - b.daysLeft);
    shortTerm.sort((a, b) => a.daysLeft - b.daysLeft);
    longTerm.sort((a, b) => a.daysLeft - b.daysLeft);

    this.setData({
      urgentWishes: urgent,
      shortTermWishes: shortTerm,
      longTermWishes: longTerm,
      noDateWishes: noDate
    });
  },

  // 切换愿望详情显示
  toggleWishDetail: function(e) {
    const wishId = e.currentTarget.dataset.id;
    const wishes = this.data.wishes.map(wish => {
      if (wish.id === wishId) {
        wish.showDetail = !wish.showDetail;
      }
      return wish;
    });

    this.setData({
      wishes: wishes
    });

    // 重新分类以更新显示
    this.categorizeWishes(wishes);
  },
  
  // 输入框内容变化
  onInputChange: function(e) {
    this.setData({
      newWish: e.detail.value
    });
  },
  
  // 添加新愿望
  addWish: function() {
    if (!this.data.newWish.trim()) {
      wx.showToast({
        title: '请输入愿望内容',
        icon: 'none'
      });
      return;
    }
    
    const wishes = this.data.wishes;
    const newWish = {
      id: Date.now().toString(),
      content: this.data.newWish.trim(),
      createTime: new Date().toISOString(),
      targetDate: this.data.newWishDate || null,
      showDetail: false
    };
    
    wishes.unshift(newWish);
    
    this.setData({
      wishes: wishes,
      newWish: '',
      newWishDate: ''
    });
    
    wx.setStorageSync('wishes', wishes);
    
    // 重新加载和分类
    this.loadWishes();
    
    wx.showToast({
      title: '愿望添加成功',
      icon: 'success'
    });

    // 如果设置了目标日期，给予鼓励
    if (newWish.targetDate) {
      setTimeout(() => {
        wx.showToast({
          title: '有目标的愿望更容易实现！',
          icon: 'none',
          duration: 2000
        });
      }, 1500);
    }
  },
  
  // 长按删除愿望
  deleteWish: function(e) {
    const wishId = e.currentTarget.dataset.id;
    const wishToDelete = this.data.wishes.find(wish => wish.id === wishId);
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除愿望"${wishToDelete.content}"吗？`,
      success: (res) => {
        if (res.confirm) {
          const wishes = this.data.wishes.filter(wish => wish.id !== wishId);
          
          this.setData({
            wishes: wishes
          });
          
          wx.setStorageSync('wishes', wishes);
          
          // 重新分类
          this.categorizeWishes(wishes);
          
          wx.showToast({
            title: '已删除',
            icon: 'success'
          });
        }
      }
    });
  }
}) 