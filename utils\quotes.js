/**
 * 金句工具函数集合
 * @module utils/quotes
 */

/**
 * 金句数据库，共31条，对应每月每日一条
 * @private
 * @type {string[]}
 */
const quotes = [
  "生命不在于长短，而在于怎样度过。 —— 塞内加",
  "每一天都是余额不足的存款，好好花。 —— 莫言",
  "人的一生可能燃烧也可能腐朽，我不能腐朽，我愿意燃烧起来。 —— 奥斯特洛夫斯基",
  "不要等到明天，明天太遥远，今天就行动。 —— 歌德",
  "生命太过短暂，今天放弃了明天不一定能得到。 —— 拿破仑",
  "时间是最公平的，给任何人都是24小时；时间也是最不公平的，它给任何人的都不是24小时。 —— 赫拉利",
  "人生有两出悲剧：一是万念俱灰，另一是踌躇满志。 —— 萧伯纳",
  "人生苦短，必须性情好，不然就太亏了。 —— 契诃夫",
  "你热爱生命吗？那么别浪费时间，因为时间是组成生命的材料。 —— 富兰克林",
  "人生最大的遗憾，不是没能得到什么，而是轻易地放弃了不该放弃的。 —— 罗曼·罗兰",
  "别把时间花在追忆过去或者憧憬未来上，集中精力做好现在的事。 —— 马克·吐温",
  "不要哀叹生命的短暂，要惋惜的是虚度的年华。 —— 富兰克林",
  "一个人最大的破产是绝望，最大的资产是希望。 —— 卡耐基",
  "世界上最快乐的事，莫过于为理想而奋斗。 —— 苏格拉底",
  "把自己的生命花费在哪里，也就把自己的价值实现在哪里。 —— 列夫·托尔斯泰",
  "生如夏花之绚烂，死如秋叶之静美。 —— 泰戈尔",
  "我们若已接受最坏的，就再没有什么损失。 —— 卡耐基",
  "比时间更有价值的东西，一定是超越了时间的东西。 —— 维特根斯坦",
  "没有人事先了解自己到底有多大的力量，直到他试过以后才知道。 —— 歌德",
  "在一个崇高的目标支持下，不停地工作，即使慢，也一定会获得成功。 —— 爱因斯坦",
  "不要在每一次错过中思念最初的心情，要在每一次相逢中点燃未来的希望。 —— 海明威",
  "不要因为跌倒而哭泣，要在哭泣之后重新站起。 —— 莎士比亚",
  "最糟糕的是人们放弃自己之前就已经放弃了努力。 —— 贝弗里奇",
  "人生就像骑单车，要保持平衡就得不断前进。 —— 爱因斯坦",
  "世界上最永恒的幸福就是平凡，最长久的拥有就是珍惜。 —— 列夫·托尔斯泰",
  "一个人可以非常清贫、困顿、低微，但是不可以没有梦想。 —— 马克思",
  "世上最累人的事，莫过于虚伪的过日子。 —— 列夫·托尔斯泰",
  "没有人能给你光明，除了你自己。 —— 罗曼·罗兰",
  "一个人如果不能从内心去原谅别人，那他就永远不会心安理得。 —— 马丁·路德·金",
  "人最可悲的是自己不能战胜自己。 —— 苏格拉底",
  "未来不属于那些满足于现状的人，而是那些致力于梦想的人。 —— 爱因斯坦"
];

/**
 * 获取随机金句
 * @returns {string} 随机金句
 * @example
 * const quote = getQuote();  // 返回随机一条金句
 */
export function getQuote() {
  // 根据当前日期选择金句（确保每天看到的金句相同）
  const date = new Date();
  const day = date.getDate() - 1; // 0-30
  return quotes[day % quotes.length];
}

/**
 * 获取指定索引的金句
 * @param {number} index 金句索引
 * @returns {string} 指定索引的金句，若索引无效则返回随机金句
 * @example
 * // 获取第一条金句
 * const firstQuote = getQuoteByIndex(0);
 * 
 * // 索引无效时返回随机金句
 * const randomQuote = getQuoteByIndex(100);
 */
export function getQuoteByIndex(index) {
  if (index >= 0 && index < quotes.length) {
    return quotes[index];
  }
  return getQuote(); // 如果索引无效，返回随机金句
} 