/**
 * 设备适配测试脚本
 * 用于验证设备适配功能的正确性
 */

// 模拟不同设备的系统信息
const mockDevices = [
  {
    name: 'iPhone SE 1st',
    windowWidth: 320,
    windowHeight: 568,
    pixelRatio: 2,
    statusBarHeight: 20,
    platform: 'ios',
    safeArea: { top: 20, bottom: 568 }
  },
  {
    name: 'iPhone 8',
    windowWidth: 375,
    windowHeight: 667,
    pixelRatio: 2,
    statusBarHeight: 20,
    platform: 'ios',
    safeArea: { top: 20, bottom: 667 }
  },
  {
    name: 'iPhone X',
    windowWidth: 375,
    windowHeight: 812,
    pixelRatio: 3,
    statusBarHeight: 44,
    platform: 'ios',
    safeArea: { top: 44, bottom: 778 }
  },
  {
    name: 'iPhone 11 Pro Max',
    windowWidth: 414,
    windowHeight: 896,
    pixelRatio: 3,
    statusBarHeight: 44,
    platform: 'ios',
    safeArea: { top: 44, bottom: 862 }
  },
  {
    name: 'iPhone 14 Pro Max',
    windowWidth: 428,
    windowHeight: 926,
    pixelRatio: 3,
    statusBarHeight: 54,
    platform: 'ios',
    safeArea: { top: 54, bottom: 892 }
  },
  {
    name: 'Android Phone',
    windowWidth: 360,
    windowHeight: 640,
    pixelRatio: 2,
    statusBarHeight: 24,
    platform: 'android',
    safeArea: { top: 24, bottom: 640 }
  },
  {
    name: 'iPad',
    windowWidth: 768,
    windowHeight: 1024,
    pixelRatio: 2,
    statusBarHeight: 20,
    platform: 'ios',
    safeArea: { top: 20, bottom: 1024 }
  }
];

// 模拟deviceUtils模块
function createMockDeviceUtils(mockSystemInfo) {
  return {
    getSystemInfo: () => mockSystemInfo,
    
    getDeviceType: () => {
      const width = mockSystemInfo.windowWidth;
      if (width <= 320) return 'small';
      if (width <= 375) return 'medium';
      if (width <= 414) return 'large';
      return 'tablet';
    },
    
    getSafeAreaInfo: () => {
      const safeArea = mockSystemInfo.safeArea || {};
      return {
        top: safeArea.top || mockSystemInfo.statusBarHeight || 20,
        bottom: safeArea.bottom || mockSystemInfo.windowHeight,
        left: safeArea.left || 0,
        right: safeArea.right || mockSystemInfo.windowWidth,
        height: (safeArea.bottom || mockSystemInfo.windowHeight) - (safeArea.top || mockSystemInfo.statusBarHeight || 20)
      };
    },
    
    isNotchScreen: () => {
      const statusBarHeight = mockSystemInfo.statusBarHeight || 20;
      const safeArea = mockSystemInfo.safeArea || {};
      return statusBarHeight > 20 || (safeArea.top && safeArea.top > statusBarHeight);
    },
    
    getResponsiveSize: (sizes) => {
      const deviceType = mockSystemInfo.windowWidth <= 320 ? 'small' :
                        mockSystemInfo.windowWidth <= 375 ? 'medium' :
                        mockSystemInfo.windowWidth <= 414 ? 'large' : 'tablet';
      return sizes[deviceType] || sizes.medium || sizes.default || 0;
    },
    
    getAdaptiveFontSize: (baseFontSize) => {
      const deviceType = mockSystemInfo.windowWidth <= 320 ? 'small' :
                        mockSystemInfo.windowWidth <= 375 ? 'medium' :
                        mockSystemInfo.windowWidth <= 414 ? 'large' : 'tablet';
      const scaleMap = { small: 0.9, medium: 1.0, large: 1.05, tablet: 1.1 };
      return Math.round(baseFontSize * (scaleMap[deviceType] || 1.0));
    },
    
    getAdaptiveSpacing: (baseSpacing) => {
      const deviceType = mockSystemInfo.windowWidth <= 320 ? 'small' :
                        mockSystemInfo.windowWidth <= 375 ? 'medium' :
                        mockSystemInfo.windowWidth <= 414 ? 'large' : 'tablet';
      const scaleMap = { small: 0.8, medium: 1.0, large: 1.1, tablet: 1.2 };
      return Math.round(baseSpacing * (scaleMap[deviceType] || 1.0));
    },
    
    getDeviceClassName: () => {
      const deviceType = mockSystemInfo.windowWidth <= 320 ? 'small' :
                        mockSystemInfo.windowWidth <= 375 ? 'medium' :
                        mockSystemInfo.windowWidth <= 414 ? 'large' : 'tablet';
      const isNotch = mockSystemInfo.statusBarHeight > 20;
      let className = `device-${deviceType}`;
      if (isNotch) className += ' device-notch';
      if (mockSystemInfo.platform === 'android') className += ' platform-android';
      else if (mockSystemInfo.platform === 'ios') className += ' platform-ios';
      return className;
    }
  };
}

// 运行测试
function runAdaptationTests() {
  console.log('🧪 开始设备适配测试...\n');
  
  mockDevices.forEach((device, index) => {
    console.log(`📱 测试设备 ${index + 1}: ${device.name}`);
    console.log(`   屏幕尺寸: ${device.windowWidth}x${device.windowHeight}`);
    
    const deviceUtils = createMockDeviceUtils(device);
    
    // 测试设备类型检测
    const deviceType = deviceUtils.getDeviceType();
    console.log(`   设备类型: ${deviceType}`);
    
    // 测试刘海屏检测
    const isNotch = deviceUtils.isNotchScreen();
    console.log(`   刘海屏: ${isNotch ? '是' : '否'}`);
    
    // 测试安全区域
    const safeArea = deviceUtils.getSafeAreaInfo();
    console.log(`   安全区域: 顶部${safeArea.top}px, 底部${device.windowHeight - safeArea.bottom}px`);
    
    // 测试响应式尺寸
    const responsiveSize = deviceUtils.getResponsiveSize({
      small: 100, medium: 120, large: 140, tablet: 160
    });
    console.log(`   响应式尺寸: ${responsiveSize}rpx`);
    
    // 测试字体适配
    const fontSize = deviceUtils.getAdaptiveFontSize(32);
    console.log(`   适配字体: ${fontSize}rpx (基础32rpx)`);
    
    // 测试间距适配
    const spacing = deviceUtils.getAdaptiveSpacing(24);
    console.log(`   适配间距: ${spacing}rpx (基础24rpx)`);
    
    // 测试CSS类名
    const className = deviceUtils.getDeviceClassName();
    console.log(`   CSS类名: ${className}`);
    
    console.log('   ✅ 测试通过\n');
  });
  
  console.log('🎉 所有设备适配测试完成！');
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAdaptationTests, mockDevices, createMockDeviceUtils };
  
  // 直接运行测试
  if (require.main === module) {
    runAdaptationTests();
  }
} else {
  // 在浏览器环境中运行
  runAdaptationTests();
}
