/**
 * 设备适配工具类
 * 用于处理不同设备的屏幕适配和响应式布局
 */

let systemInfo = null;

/**
 * 获取系统信息
 * @returns {Object} 系统信息对象
 */
function getSystemInfo() {
  if (!systemInfo) {
    try {
      systemInfo = wx.getSystemInfoSync();
    } catch (e) {
      console.error('获取系统信息失败:', e);
      // 提供默认值
      systemInfo = {
        windowWidth: 375,
        windowHeight: 667,
        pixelRatio: 2,
        statusBarHeight: 20,
        safeArea: {
          top: 20,
          bottom: 667
        },
        model: 'iPhone',
        platform: 'ios'
      };
    }
  }
  return systemInfo;
}

/**
 * 获取设备类型
 * @returns {string} 设备类型：'small', 'medium', 'large', 'tablet'
 */
function getDeviceType() {
  const info = getSystemInfo();
  const width = info.windowWidth;
  
  if (width <= 320) return 'small';      // 小屏设备 (iPhone SE 1st)
  if (width <= 375) return 'medium';     // 中等屏幕 (iPhone 6/7/8, iPhone X/XS)
  if (width <= 414) return 'large';      // 大屏手机 (iPhone 6+/7+/8+, iPhone XR/11)
  return 'tablet';                       // 平板或超大屏
}

/**
 * 获取安全区域信息
 * @returns {Object} 安全区域信息
 */
function getSafeAreaInfo() {
  const info = getSystemInfo();
  const safeArea = info.safeArea || {};
  
  return {
    top: safeArea.top || info.statusBarHeight || 20,
    bottom: safeArea.bottom || info.windowHeight,
    left: safeArea.left || 0,
    right: safeArea.right || info.windowWidth,
    height: (safeArea.bottom || info.windowHeight) - (safeArea.top || info.statusBarHeight || 20)
  };
}

/**
 * 判断是否为刘海屏
 * @returns {boolean} 是否为刘海屏
 */
function isNotchScreen() {
  const info = getSystemInfo();
  const safeArea = info.safeArea || {};
  const statusBarHeight = info.statusBarHeight || 20;
  
  // 刘海屏的状态栏高度通常大于20px，且安全区域顶部大于状态栏高度
  return statusBarHeight > 20 || (safeArea.top && safeArea.top > statusBarHeight);
}

/**
 * 获取响应式尺寸
 * @param {Object} sizes 不同设备类型的尺寸配置
 * @returns {number} 适配后的尺寸
 */
function getResponsiveSize(sizes) {
  const deviceType = getDeviceType();
  return sizes[deviceType] || sizes.medium || sizes.default || 0;
}

/**
 * rpx转px
 * @param {number} rpx rpx值
 * @returns {number} px值
 */
function rpxToPx(rpx) {
  const info = getSystemInfo();
  return rpx * info.windowWidth / 750;
}

/**
 * px转rpx
 * @param {number} px px值
 * @returns {number} rpx值
 */
function pxToRpx(px) {
  const info = getSystemInfo();
  return px * 750 / info.windowWidth;
}

/**
 * 获取Canvas适配参数
 * @param {Object} canvasInfo Canvas节点信息
 * @returns {Object} 适配参数
 */
function getCanvasAdaptParams(canvasInfo) {
  const info = getSystemInfo();
  const dpr = info.pixelRatio || 2;
  
  return {
    dpr: dpr,
    width: canvasInfo.width,
    height: canvasInfo.height,
    canvasWidth: canvasInfo.width * dpr,
    canvasHeight: canvasInfo.height * dpr,
    // 根据设备类型调整Canvas内容尺寸
    contentScale: getResponsiveSize({
      small: 0.8,
      medium: 1.0,
      large: 1.1,
      tablet: 1.2
    })
  };
}

/**
 * 获取字体大小适配
 * @param {number} baseFontSize 基础字体大小(rpx)
 * @returns {number} 适配后的字体大小(rpx)
 */
function getAdaptiveFontSize(baseFontSize) {
  const deviceType = getDeviceType();
  const scaleMap = {
    small: 0.9,
    medium: 1.0,
    large: 1.05,
    tablet: 1.1
  };
  
  return Math.round(baseFontSize * (scaleMap[deviceType] || 1.0));
}

/**
 * 获取间距适配
 * @param {number} baseSpacing 基础间距(rpx)
 * @returns {number} 适配后的间距(rpx)
 */
function getAdaptiveSpacing(baseSpacing) {
  const deviceType = getDeviceType();
  const scaleMap = {
    small: 0.8,
    medium: 1.0,
    large: 1.1,
    tablet: 1.2
  };
  
  return Math.round(baseSpacing * (scaleMap[deviceType] || 1.0));
}

/**
 * 获取设备特定的样式类名
 * @returns {string} 样式类名
 */
function getDeviceClassName() {
  const deviceType = getDeviceType();
  const info = getSystemInfo();
  const isNotch = isNotchScreen();
  
  let className = `device-${deviceType}`;
  
  if (isNotch) {
    className += ' device-notch';
  }
  
  if (info.platform === 'android') {
    className += ' platform-android';
  } else if (info.platform === 'ios') {
    className += ' platform-ios';
  }
  
  return className;
}

module.exports = {
  getSystemInfo,
  getDeviceType,
  getSafeAreaInfo,
  isNotchScreen,
  getResponsiveSize,
  rpxToPx,
  pxToRpx,
  getCanvasAdaptParams,
  getAdaptiveFontSize,
  getAdaptiveSpacing,
  getDeviceClassName
};
