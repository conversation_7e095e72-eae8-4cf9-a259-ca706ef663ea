# 倒数人生微信小程序

一个计算生命进度和倒计时的微信小程序，帮助用户更好地规划时间、设定目标。

![应用图标](./icon.png)

## 功能特性

1. **生命进度计算**：基于用户出生日期，计算已活天数与剩余天数
2. **可定制寿命**：默认30000天（约82年），可在设置中调整
3. **环形进度条**：直观展示生命进度百分比
4. **每日金句**：提供激励人心的名言
5. **愿望清单**：记录人生想要完成的事情
6. **一键分享**：生成分享图片，分享到社交媒体
7. **重要节点提醒**：在生命剩余10000/5000/1000天等重要节点提醒用户

## 页面导航

- **首页**：输入出生日期，开始计算
- **结果页**：展示生命进度、剩余天数和金句
- **愿望清单**：添加和管理人生愿望
- **设置页**：自定义期望寿命

## 技术实现

- 使用小程序原生框架开发
- Canvas 2D绘制环形进度条和分享图
- 本地存储保存用户设置和愿望清单
- **全面设备适配系统**：支持所有主流手机型号
- **响应式布局**：自动适配不同屏幕尺寸和像素密度
- **刘海屏优化**：完美适配iPhone X系列及以后机型

## 图标说明

- 主图标：蓝色霓虹风格的人物轮廓，显示生命进度
- TabBar图标：使用SVG格式的简洁图标，区分不同功能区域

## 设备兼容性

### 📱 支持的设备类型
- **小屏设备** (≤320px): iPhone SE 1st generation
- **中等屏幕** (321-375px): iPhone 6/7/8, iPhone X/XS
- **大屏手机** (376-414px): iPhone 6+/7+/8+, iPhone XR/11, iPhone 12/13/14
- **超大屏/平板** (>414px): iPhone 12/13/14 Pro Max, iPad, Android平板

### 🔧 特殊适配
- ✅ 刘海屏设备完美适配
- ✅ 横屏模式优化
- ✅ 安全区域自动处理
- ✅ 高分辨率屏幕支持
- ✅ Android设备兼容

## 安装和使用

1. 使用微信开发者工具导入项目
2. 编译并预览
3. 扫码即可在手机上体验
4. 在设置页面可以测试设备适配效果

## 关于

本项目旨在帮助人们更好地认识时间的宝贵，珍惜当下，规划未来。

> "生命不在于长短，而在于怎样度过。" 