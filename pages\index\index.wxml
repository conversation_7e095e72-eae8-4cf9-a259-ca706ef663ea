<!--pages/index/index.wxml-->
<view class="container {{deviceClassName}}">
  <view class="title">倒数人生</view>
  <view class="subtitle">珍惜时间，感悟人生</view>

  <view class="futuristic-card">
    <view class="input-container">
      <picker mode="date" value="{{birthDate}}" start="1900-01-01" bindchange="onBirthDateChange">
        <view class="picker-view">
          <text class="placeholder">{{birthDate || '请选择生日'}}</text>
          <view class="date-icon">📅</view>
        </view>
      </picker>
    </view>

    <!-- 生命统计显示 -->
    <view class="life-stats" wx:if="{{birthDate}}">
      <view class="stat-item">
        <text class="stat-label">已生存</text>
        <text class="stat-value">{{livedDays}}</text>
        <text class="stat-unit">天</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-label">剩余预期</text>
        <text class="stat-value">{{remainingDays}}</text>
        <text class="stat-unit">天</text>
      </view>
    </view>

    <button class="calculate-btn"
            bindtap="startCountdown"
            disabled="{{!birthDate}}">
      开始计算
    </button>
  </view>

  <view class="footer-text">
    <text class="quote">珍惜每一天，活出精彩人生</text>
  </view>
</view>