<!--pages/index/index.wxml-->
<view class="container {{animationEnter ? 'page-enter' : ''}} {{animationExit ? 'page-exit' : ''}}" bindtouchstart="onPageTouch">
  <view class="futuristic-bg">
    <!-- 星空背景 -->
    <view class="starfield">
      <view class="star" wx:for="{{stars}}" wx:key="id" style="left: {{item.x}}%; top: {{item.y}}%; width: {{item.size}}rpx; height: {{item.size}}rpx; opacity: {{item.opacity}}; animation-duration: {{item.duration}}s; animation-delay: {{item.delay}}s;"></view>
    </view>
    
    <!-- 添加悬浮粒子 -->
    <view class="floating-particles">
      <view class="particle" wx:for="{{particles}}" wx:key="index" style="left: {{item.left}}; top: {{item.top}}; width: {{item.size}}; height: {{item.size}}; animation-delay: {{item.animationDelay}}; animation-duration: {{item.animationDuration}};"></view>
    </view>
    
    <!-- 添加技术线条 -->
    <view class="tech-lines">
      <view class="tech-line" style="top: 10%; animation-delay: 0s;"></view>
      <view class="tech-line" style="top: 25%; animation-delay: 3s;"></view>
      <view class="tech-line" style="top: 40%; animation-delay: 6s;"></view>
      <view class="tech-line" style="top: 55%; animation-delay: 9s;"></view>
      <view class="tech-line" style="top: 70%; animation-delay: 12s;"></view>
      <view class="tech-line" style="top: 85%; animation-delay: 15s;"></view>
    </view>
    
    <!-- 光效跟随 -->
    <view class="glow-follower" style="left: {{glowPosition.x}}px; top: {{glowPosition.y}}px; opacity: {{glowPosition.opacity}};"></view>
    
    <!-- 数字雨效果 -->
    <view class="digital-rain">
      <view class="rain-column" wx:for="{{digitalRain}}" wx:key="index" style="left: {{item.left}}%; animation-delay: {{item.delay}}s; animation-duration: {{item.duration}}s;">
        <text class="rain-char" wx:for="{{item.chars}}" wx:key="idx">{{item}}</text>
      </view>
    </view>
  </view>
  
  <!-- 浮动标签 -->
  <view class="floating-labels">
    <view class="floating-label" wx:for="{{floatingLabels}}" wx:key="id" style="left: {{item.x}}%; top: {{item.y}}%; opacity: {{item.opacity}};">
      <text>{{item.text}}</text>
    </view>
  </view>
  <view class="title neon-text" data-text="倒数人生">倒数人生</view>
  <view class="subtitle {{subtitleVisible ? 'subtitle-show' : ''}}">{{currentSubtitle}}</view>
  
  <view class="futuristic-card card-3d" style="{{cardStyle}}" 
    bindtouchstart="onCardTouchStart" 
    bindtouchmove="onCardTouchMove" 
    bindtouchend="onCardTouchEnd"
    bindtouchcancel="onCardTouchEnd">
    
    <!-- 卡片光效 -->
    <view class="card-glow"></view>
    
    <view class="input-container">
      <picker mode="date" value="{{birthDate}}" start="1900-01-01" bindchange="onBirthDateChange">
        <view class="picker-view neon-border">
          <text class="placeholder">{{birthDate || '请选择生日'}}</text>
          <view class="date-icon">📅</view>
        </view>
      </picker>
    </view>
    
    <!-- 生命统计显示 -->
    <view class="life-stats" wx:if="{{birthDate}}">
      <view class="stat-item">
        <text class="stat-label">已生存</text>
        <text class="stat-value neon-text">{{livedDays}}</text>
        <text class="stat-unit">天</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-label">剩余预期</text>
        <text class="stat-value neon-text">{{remainingDays}}</text>
        <text class="stat-unit">天</text>
      </view>
    </view>
    
    <button class="calculate-btn {{isAnimating ? 'button-pressed' : ''}}" 
            bindtap="startCountdown" 
            disabled="{{!birthDate}}">
      <text>{{buttonText}}</text>
      <view class="btn-glow"></view>
      
      <!-- 按钮波纹效果 -->
      <view class="ripple" wx:if="{{showRipple}}" style="left: {{ripplePosition.x}}px; top: {{ripplePosition.y}}px;"></view>
    </button>
  </view>
  
    <!-- 加载动画 -->  <view class="loading-overlay" wx:if="{{showLoading}}">    <view class="loading-content">      <!-- 治愈简笔画 -->      <view class="healing-image">        <view class="simple-drawing">{{currentHealingImage}}</view>      </view>      <text class="loading-text">{{loadingText}}</text>    </view>  </view>
  
  <view class="footer-text">
    <text class="quote">「{{currentQuote}}」</text>
  </view>
</view> 