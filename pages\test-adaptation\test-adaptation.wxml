<!--pages/test-adaptation/test-adaptation.wxml-->
<view class="container {{deviceClassName}}">
  <view class="title">界面效果展示</view>

  <view class="glass-card">
    <view class="section-title">现代卡片设计</view>
    <view class="demo-content">
      <text class="demo-text">这是一个现代化的玻璃拟态卡片，具有美观的背景模糊效果和渐变边框。</text>
    </view>
  </view>

  <view class="glass-card">
    <view class="section-title">设备信息</view>
    <view class="info-grid">
      <view class="info-item">
        <text class="label">设备型号</text>
        <text class="value">{{systemInfo.model}}</text>
      </view>
      <view class="info-item">
        <text class="label">屏幕尺寸</text>
        <text class="value">{{systemInfo.windowWidth}}×{{systemInfo.windowHeight}}</text>
      </view>
      <view class="info-item">
        <text class="label">设备类型</text>
        <text class="value">{{deviceType}}</text>
      </view>
      <view class="info-item">
        <text class="label">像素比</text>
        <text class="value">{{systemInfo.pixelRatio}}</text>
      </view>
    </view>
  </view>

  <view class="glass-card">
    <view class="section-title">视觉效果测试</view>
    <view class="visual-demo">
      <view class="gradient-box">
        <text class="gradient-text">渐变文字效果</text>
      </view>
      <view class="glow-box">
        <text class="glow-text">发光文字效果</text>
      </view>
      <view class="shadow-box">
        <text class="shadow-text">阴影效果</text>
      </view>
    </view>
  </view>

  <view class="actions">
    <button class="btn-primary" bindtap="backToHome">返回首页</button>
  </view>
</view>
