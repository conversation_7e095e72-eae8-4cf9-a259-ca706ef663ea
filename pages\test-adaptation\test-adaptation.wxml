<!--pages/test-adaptation/test-adaptation.wxml-->
<view class="container {{deviceClassName}}">
  <view class="title">设备适配测试</view>
  
  <view class="glass-card">
    <view class="section-title">设备信息</view>
    <view class="info-item">
      <text class="label">设备型号:</text>
      <text class="value">{{systemInfo.model}}</text>
    </view>
    <view class="info-item">
      <text class="label">操作系统:</text>
      <text class="value">{{systemInfo.platform}} {{systemInfo.system}}</text>
    </view>
    <view class="info-item">
      <text class="label">微信版本:</text>
      <text class="value">{{systemInfo.version}}</text>
    </view>
    <view class="info-item">
      <text class="label">设备类名:</text>
      <text class="value">{{deviceClassName}}</text>
    </view>
  </view>

  <view class="glass-card">
    <view class="section-title">适配测试结果</view>
    <view class="test-results">
      <view class="test-item" wx:for="{{testResults}}" wx:key="name">
        <text class="test-label">{{item.name}}:</text>
        <text class="test-value">{{item.value}}</text>
      </view>
    </view>
  </view>

  <view class="glass-card">
    <view class="section-title">视觉测试</view>
    <view class="visual-test">
      <view class="test-box small">小</view>
      <view class="test-box medium">中</view>
      <view class="test-box large">大</view>
    </view>
    <view class="test-text">
      这是一段测试文字，用于验证字体大小和行高的适配效果。在不同设备上应该有合适的显示效果。
    </view>
  </view>

  <view class="actions">
    <button class="btn-primary" bindtap="backToHome">返回首页</button>
  </view>
</view>
