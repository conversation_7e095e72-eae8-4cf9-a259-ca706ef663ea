// 倒数人生小程序索引文件
// 该文件作为应用功能的导航索引

/**
 * 应用主要页面索引
 */
const pageIndex = {
  // 主页 - 输入生日信息
  index: '/pages/index/index',
  
  // 结果页 - 显示生命进度
  result: '/pages/result/result',
  
  // 愿望清单 - 记录想要完成的事情
  wishes: '/pages/wishes/wishes',
  
  // 设置页 - 配置预期寿命等
  settings: '/pages/settings/settings'
};

/**
 * 主要功能索引
 */
const functionIndex = {
  // 计算生命进度
  calculateLifeProgress: function(birthDate, targetDays = 30000) {
    const birthTimestamp = new Date(birthDate).getTime();
    const today = new Date().getTime();
    const livedDays = Math.floor((today - birthTimestamp) / (24 * 60 * 60 * 1000));
    const percentage = parseFloat((livedDays / targetDays * 100).toFixed(2));
    const remainingDays = targetDays - livedDays;
    
    return {
      livedDays,
      remainingDays,
      percentage,
      targetDays
    };
  },
  
  // 获取每日金句
  getDailyQuote: function() {
    const quotes = [
      "生命不在于长短，而在于怎样度过。",
      "把每一天都当作生命的最后一天。",
      "时间是构成生命的材料。",
      "今天所做之事勿候明天，自己所做之事勿候他人。",
      "人的一生可能燃烧也可能腐朽，我不能腐朽，我愿意燃烧起来！"
    ];
    
    return quotes[Math.floor(Math.random() * quotes.length)];
  },
  
  // 保存用户设置
  saveUserSettings: function(settings) {
    try {
      wx.setStorageSync('targetDays', settings.targetDays || 30000);
      return true;
    } catch (e) {
      console.error('保存设置失败:', e);
      return false;
    }
  },
  
  // 获取用户设置
  getUserSettings: function() {
    try {
      const targetDays = wx.getStorageSync('targetDays') || 30000;
      return { targetDays };
    } catch (e) {
      console.error('获取设置失败:', e);
      return { targetDays: 30000 };
    }
  }
};

module.exports = {
  pages: pageIndex,
  functions: functionIndex
}; 