/* pages/index/index.wxss - 简洁版 */

/* 容器 */
.container {
  padding: calc(env(safe-area-inset-top) + 40rpx) clamp(32rpx, 6vw, 48rpx) calc(env(safe-area-inset-bottom) + 120rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  max-width: 750rpx;
  margin: 0 auto;
  position: relative;
}

/* 标题 */
.title {
  font-size: clamp(56rpx, 12vw, 80rpx);
  font-weight: 700;
  margin: 60rpx 0 24rpx;
  letter-spacing: 3rpx;
  text-align: center;
  line-height: 1.2;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--accent-500) 50%, var(--primary-500) 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40rpx rgba(100, 255, 218, 0.3);
  position: relative;
}

.title::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 3rpx;
  background: linear-gradient(90deg, transparent, var(--accent-500), transparent);
  border-radius: 2rpx;
}

/* 副标题 */
.subtitle {
  font-size: clamp(26rpx, 5.5vw, 32rpx);
  color: var(--text-secondary);
  margin-bottom: 48rpx;
  text-align: center;
  font-weight: 500;
  line-height: 1.6;
  padding: 0 40rpx;
  opacity: 0.9;
  letter-spacing: 1rpx;
}

/* 卡片 */
.futuristic-card {
  width: 100%;
  max-width: 640rpx;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius-card);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.futuristic-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, var(--accent-500) 50%, transparent 100%);
  opacity: 0.8;
}

.futuristic-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 0%, rgba(100, 255, 218, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.futuristic-card:hover {
  background: var(--card-hover);
  border-color: var(--accent-500);
  box-shadow: var(--shadow-glow);
  transform: translateY(-6rpx);
}

/* 输入容器 */
.input-container {
  width: 100%;
  margin-bottom: var(--spacing-lg);
}

/* 选择器样式 */
.picker-view {
  width: 100%;
  background: var(--surface-primary);
  border: 2px solid var(--card-border);
  border-radius: var(--border-radius-input);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all var(--transition-smooth);
  position: relative;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.picker-view::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--border-radius-input);
  padding: 2px;
  background: linear-gradient(135deg, var(--accent-500), var(--primary-500));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity var(--transition-smooth);
}

.picker-view:active {
  background: var(--card-hover);
  border-color: var(--accent-500);
  transform: scale(0.98);
  box-shadow: var(--shadow-glow);
}

.picker-view:active::before {
  opacity: 1;
}

.placeholder {
  color: var(--text-primary);
  font-size: 30rpx;
  font-weight: 500;
}

.date-icon {
  font-size: 36rpx;
  color: var(--accent-500);
}

/* 生命统计显示 */
.life-stats {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  border-radius: var(--border-radius-card);
  border: 1px solid var(--card-border);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.life-stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--accent-500) 50%, transparent 100%);
  opacity: 0.8;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-tertiary);
  margin-bottom: 12rpx;
  font-weight: 500;
  letter-spacing: 1rpx;
  text-transform: uppercase;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-accent);
  margin-bottom: 6rpx;
  text-shadow: 0 0 20rpx rgba(100, 255, 218, 0.4);
}

.stat-unit {
  font-size: 22rpx;
  color: var(--text-muted);
  font-weight: 500;
}

.stat-divider {
  width: 2px;
  height: 60rpx;
  background: linear-gradient(180deg, transparent 0%, var(--accent-500) 50%, transparent 100%);
  margin: 0 var(--spacing-lg);
  border-radius: 1px;
}

/* 按钮 */
.calculate-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--accent-500) 100%);
  border: none;
  border-radius: var(--border-radius-btn);
  color: var(--text-primary);
  font-weight: 600;
  font-size: 34rpx;
  letter-spacing: 2rpx;
  transition: all var(--transition-smooth);
  box-shadow: var(--shadow-primary);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
}

.calculate-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.8s ease;
}

.calculate-btn:hover {
  background: linear-gradient(135deg, var(--accent-500) 0%, var(--primary-500) 100%);
  box-shadow: var(--shadow-glow), var(--shadow-lg);
  transform: translateY(-6rpx);
}

.calculate-btn:hover::before {
  left: 100%;
}

.calculate-btn:active {
  transform: translateY(-3rpx) scale(0.98);
  box-shadow: var(--shadow-primary);
}

.calculate-btn:disabled {
  background: var(--surface-secondary);
  color: var(--text-muted);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
  opacity: 0.6;
}

/* 底部文字 */
.footer-text {
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom) + 80rpx);
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  width: 90%;
  z-index: 10;
  padding: var(--spacing-md);
  background: rgba(15, 32, 39, 0.8);
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(100, 255, 218, 0.1);
}

.quote {
  font-size: 26rpx;
  color: var(--text-tertiary);
  line-height: 1.6;
  font-style: italic;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

/* 设备特定样式 */
.device-small .title {
  font-size: clamp(38rpx, 8vw, 46rpx) !important;
  letter-spacing: 1rpx !important;
  margin-bottom: clamp(20rpx, 4vw, 28rpx) !important;
}

.device-small .subtitle {
  font-size: clamp(16rpx, 3.5vw, 20rpx) !important;
  margin-bottom: clamp(40rpx, 8vw, 60rpx) !important;
}

.device-small .futuristic-card {
  padding: clamp(20rpx, 4vw, 32rpx) !important;
  margin-bottom: clamp(16rpx, 3vw, 24rpx) !important;
}

.device-small .life-stats {
  flex-direction: column;
  gap: clamp(16rpx, 3vw, 24rpx);
  padding: clamp(20rpx, 4vw, 28rpx);
}

.device-small .stat-divider {
  width: 60rpx;
  height: 1px;
  margin: 0;
}

.device-small .calculate-btn {
  font-size: clamp(26rpx, 5vw, 32rpx) !important;
  line-height: clamp(70rpx, 14vw, 88rpx) !important;
}

/* 中等设备样式 */
.device-medium .title {
  font-size: clamp(48rpx, 9vw, 60rpx) !important;
  letter-spacing: 2rpx !important;
}

.device-medium .subtitle {
  font-size: clamp(20rpx, 4vw, 26rpx) !important;
}

.device-medium .futuristic-card {
  padding: clamp(28rpx, 5vw, 40rpx) !important;
}

/* 大屏设备样式 */
.device-large .title {
  font-size: clamp(52rpx, 10vw, 68rpx) !important;
  letter-spacing: 3rpx !important;
}

.device-large .subtitle {
  font-size: clamp(22rpx, 4.5vw, 28rpx) !important;
}

.device-large .futuristic-card {
  padding: clamp(32rpx, 6vw, 48rpx) !important;
  max-width: 650rpx !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* 平板设备样式 */
.device-tablet .container {
  max-width: 800rpx !important;
  margin: 0 auto !important;
}

.device-tablet .title {
  font-size: clamp(60rpx, 12vw, 80rpx) !important;
  letter-spacing: 4rpx !important;
}

.device-tablet .subtitle {
  font-size: clamp(24rpx, 5vw, 32rpx) !important;
}

.device-tablet .futuristic-card {
  padding: clamp(40rpx, 7vw, 60rpx) !important;
  max-width: 700rpx !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* 响应式适配 */
@media screen and (max-width: 320px) {
  .container {
    padding: calc(env(safe-area-inset-top) + 40rpx) clamp(16rpx, 4vw, 24rpx) calc(env(safe-area-inset-bottom) + 100rpx);
  }
  
  .footer-text {
    bottom: calc(env(safe-area-inset-bottom) + 40rpx);
    padding: 0 clamp(16rpx, 4vw, 24rpx);
  }
  
  .quote {
    font-size: clamp(18rpx, 3.5vw, 22rpx) !important;
    line-height: 1.4 !important;
  }
}

@media screen and (max-width: 375px) {
  .container {
    padding: calc(env(safe-area-inset-top) + 45rpx) clamp(20rpx, 4vw, 32rpx) calc(env(safe-area-inset-bottom) + 110rpx);
  }
  
  .life-stats {
    padding: clamp(20rpx, 4vw, 32rpx) clamp(12rpx, 2.5vw, 20rpx);
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
  .container {
    padding: calc(env(safe-area-inset-top) + 20rpx) clamp(32rpx, 8vw, 64rpx) calc(env(safe-area-inset-bottom) + 60rpx);
    max-width: 90vw;
    margin: 0 auto;
  }
  
  .futuristic-card {
    max-width: none;
    margin-bottom: clamp(16rpx, 3vw, 24rpx);
    padding: clamp(24rpx, 4vw, 40rpx);
  }
  
  .title {
    font-size: clamp(36rpx, 6vw, 48rpx);
    margin-bottom: clamp(16rpx, 3vw, 24rpx);
  }
  
  .subtitle {
    font-size: clamp(20rpx, 4vw, 24rpx);
    margin-bottom: clamp(24rpx, 5vw, 32rpx);
  }
  
  .calculate-btn {
    line-height: clamp(60rpx, 12vw, 80rpx);
    font-size: clamp(28rpx, 5vw, 36rpx);
  }
}
