/* pages/index/index.wxss - 未来科技版 */
.container {
  padding: calc(env(safe-area-inset-top) + 60rpx) clamp(40rpx, 8vw, 60rpx) calc(env(safe-area-inset-bottom) + 120rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  animation: page-quantum-enter 1.2s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
}

/* 量子传送进入动画 */
@keyframes page-quantum-enter {
  0% {
    opacity: 0;
    transform: scale(0.8) rotateY(45deg);
    filter: blur(20rpx);
  }
  60% {
    opacity: 0.8;
    transform: scale(1.05) rotateY(0deg);
    filter: blur(4rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotateY(0deg);
    filter: blur(0);
  }
}

/* 简化的动态背景 */
.quantum-field {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
  overflow: hidden;
}

/* 能量粒子 */
.energy-particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: radial-gradient(circle, #00D9FF 0%, transparent 70%);
  border-radius: 50%;
  animation: particle-flow infinite linear;
  box-shadow: 0 0 20rpx #00D9FF;
}

@keyframes particle-flow {
  0% {
    transform: translateY(100vh) translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translateY(90vh) translateX(10rpx) scale(1);
  }
  90% {
    opacity: 0.8;
    transform: translateY(10vh) translateX(-10rpx) scale(1.2);
  }
  100% {
    transform: translateY(0) translateX(0) scale(0);
    opacity: 0;
  }
}

/* 简洁标题 */
.title {
  font-size: clamp(56rpx, 12vw, 80rpx);
  font-weight: 200;
  margin: 60rpx 0 20rpx;
  letter-spacing: 8rpx;
  text-align: center;
  line-height: 1.2;
  position: relative;
  background: linear-gradient(135deg, 
    #00D9FF 0%, 
    #8A2BE2 35%, 
    #FF0080 70%, 
    #00D9FF 100%);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: title-flow 4s ease-in-out infinite;
  text-shadow: 0 0 40rpx rgba(0, 217, 255, 0.3);
}

@keyframes title-flow {
  0%, 100% {
    background-position: 0% 50%;
    filter: brightness(1) saturate(1);
  }
  50% {
    background-position: 100% 50%;
    filter: brightness(1.4) saturate(1.8);
  }
}

/* 优雅副标题 */
.subtitle {
  font-size: clamp(24rpx, 5vw, 32rpx);
  color: var(--text-secondary);
  margin-bottom: 80rpx;
  text-align: center;
  font-weight: 300;
  letter-spacing: 2rpx;
  opacity: 0.9;
  line-height: 1.6;
  transition: all 0.6s ease;
  padding: 0 40rpx;
  position: relative;
}

.subtitle::before {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -20rpx;
  width: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #00D9FF, transparent);
  transform: translateX(-50%);
  transition: width 0.8s ease;
}

.subtitle-show {
  animation: subtitle-appear 0.8s ease-out forwards;
}

.subtitle-show::before {
  width: 200rpx;
}

@keyframes subtitle-appear {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
    filter: blur(8rpx);
  }
  100% {
    opacity: 0.9;
    transform: translateY(0);
    filter: blur(0);
  }
}

/* 卡片容器简化 */
.card {
  width: 100%;
  max-width: 680rpx;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(0, 217, 255, 0.1);
  border-radius: 24rpx;
  backdrop-filter: blur(30rpx);
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 
    0 8rpx 40rpx rgba(0, 0, 0, 0.3),
    0 0 60rpx rgba(0, 217, 255, 0.1);
}

.card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, 
    rgba(0, 217, 255, 0.03) 0%, 
    transparent 40%, 
    rgba(138, 43, 226, 0.02) 60%, 
    transparent 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.card:hover {
  border-color: rgba(0, 217, 255, 0.3);
  transform: translateY(-8rpx) scale(1.02);
  box-shadow: 
    0 20rpx 60rpx rgba(0, 0, 0, 0.4),
    0 0 100rpx rgba(0, 217, 255, 0.2);
}

.card:hover::before {
  opacity: 1;
}

/* 简洁表单 */
.form-group {
  margin-bottom: 50rpx;
  width: 100%;
}

.label {
  display: block;
  font-size: 28rpx;
  font-weight: 400;
  color: var(--text-primary);
  margin-bottom: 20rpx;
  letter-spacing: 1rpx;
  opacity: 0.9;
}

.input {
  width: 100%;
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid rgba(0, 217, 255, 0.15);
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  color: var(--text-primary);
  font-size: 32rpx;
  font-weight: 300;
  transition: all 0.4s ease;
  backdrop-filter: blur(10rpx);
  box-sizing: border-box;
  position: relative;
}

.input:focus {
  border-color: #00D9FF;
  background: rgba(0, 217, 255, 0.05);
  outline: none;
  box-shadow: 
    0 0 0 4rpx rgba(0, 217, 255, 0.1),
    0 0 40rpx rgba(0, 217, 255, 0.2);
  transform: scale(1.02);
}

.input::placeholder {
  color: var(--text-tertiary);
  font-style: italic;
  opacity: 0.7;
}

/* 量子按钮升级 */
.submit-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, 
    #00D9FF 0%, 
    #8A2BE2 50%, 
    #FF0080 100%);
  border: none;
  border-radius: 20rpx;
  color: #FFFFFF;
  font-weight: 500;
  font-size: 36rpx;
  letter-spacing: 4rpx;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 
    0 8rpx 30rpx rgba(0, 217, 255, 0.3),
    0 0 60rpx rgba(0, 217, 255, 0.2);
}

.submit-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    transparent 50%, 
    rgba(255, 255, 255, 0.1) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.submit-btn:hover {
  transform: translateY(-4rpx) scale(1.05);
  box-shadow: 
    0 15rpx 50rpx rgba(0, 217, 255, 0.4),
    0 0 80rpx rgba(138, 43, 226, 0.3);
}

.submit-btn:hover::before {
  transform: translateX(100%);
}

.submit-btn:active {
  transform: translateY(-2rpx) scale(1.02);
  box-shadow: 
    0 8rpx 30rpx rgba(0, 217, 255, 0.3),
    0 0 60rpx rgba(255, 0, 128, 0.2);
}

.submit-btn:disabled {
  background: linear-gradient(135deg, #2A2A2A, #1A1A1A);
  color: #666666;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* 加载状态 */
.loading {
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40rpx;
  height: 40rpx;
  margin: -20rpx 0 0 -20rpx;
  border: 4rpx solid rgba(0, 217, 255, 0.3);
  border-top: 4rpx solid #00D9FF;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式优化 */
@media (max-width: 400px) {
  .container {
    padding-left: 30rpx;
    padding-right: 30rpx;
  }
  
  .card {
    padding: 40rpx 30rpx;
  }
  
  .title {
    font-size: 48rpx;
    letter-spacing: 4rpx;
  }
  
  .subtitle {
    font-size: 22rpx;
    padding: 0 20rpx;
  }
}

/* 辅助动画类 */
.fade-in {
  animation: fade-in-smooth 0.8s ease-out forwards;
}

@keyframes fade-in-smooth {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.scale-in {
  animation: scale-in-bounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

@keyframes scale-in-bounce {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* pages/index/index.wxss */
.container {
  padding: calc(env(safe-area-inset-top) + 60rpx) 5vw calc(env(safe-area-inset-bottom) + 120rpx) 5vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  animation: page-enter 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 量子星空背景 */
.starfield {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  pointer-events: none;
}

.star {
  position: absolute;
  background: radial-gradient(circle, rgba(0, 255, 255, 1) 0%, rgba(157, 78, 221, 0.8) 50%, transparent 100%);
  border-radius: 50%;
  animation: star-quantum-twinkle infinite ease-in-out alternate;
  box-shadow: 0 0 20rpx rgba(0, 255, 255, 0.8);
}

@keyframes star-quantum-twinkle {
  0% { 
    opacity: 0.3; 
    transform: scale(0.8);
    filter: hue-rotate(0deg);
  }
  100% { 
    opacity: 1; 
    transform: scale(1.2);
    filter: hue-rotate(120deg);
  }
}

/* 数字雨效果 - 时空版 */
.digital-rain {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  pointer-events: none;
  overflow: hidden;
}

.rain-column {
  position: absolute;
  top: -100%;
  width: 20rpx;
  animation: quantum-rain-fall infinite linear;
}

@keyframes quantum-rain-fall {
  0% { 
    transform: translateY(0); 
    opacity: 0;
  }
  10% { 
    opacity: 1;
  }
  90% { 
    opacity: 0.3;
  }
  100% { 
    transform: translateY(100vh); 
    opacity: 0;
  }
}

.rain-char {
  display: block;
  font-size: 24rpx;
  background: linear-gradient(180deg, 
    rgba(0, 255, 255, 0.8) 0%, 
    rgba(157, 78, 221, 0.6) 50%, 
    rgba(255, 20, 147, 0.4) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  line-height: 40rpx;
  text-shadow: 0 0 10rpx rgba(0, 255, 255, 0.8);
  animation: text-glow 2s ease-in-out infinite alternate;
}

@keyframes text-glow {
  0% { filter: brightness(0.8) saturate(1); }
  100% { filter: brightness(1.2) saturate(1.5); }
}

/* 浮动量子标签 */
.floating-labels {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  pointer-events: none;
}

.floating-label {
  position: absolute;
  padding: 10rpx 20rpx;
  background: linear-gradient(45deg, 
    rgba(0, 255, 255, 0.3), 
    rgba(157, 78, 221, 0.3));
  border: 2rpx solid rgba(0, 255, 255, 0.6);
  border-radius: 25rpx;
  color: #00FFFF;
  font-size: 24rpx;
  animation: quantum-float-up 2s ease-out forwards;
  backdrop-filter: blur(8rpx);
  box-shadow: 
    0 0 20rpx rgba(0, 255, 255, 0.4),
    inset 0 0 10rpx rgba(0, 255, 255, 0.2);
}

@keyframes quantum-float-up {
  0% {
    opacity: 0;
    transform: translateY(20rpx) scale(0.8) rotateZ(-5deg);
  }
  20% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateZ(0deg);
  }
  80% {
    opacity: 1;
    transform: translateY(-50rpx) scale(1.1) rotateZ(5deg);
  }
  100% {
    opacity: 0;
    transform: translateY(-100rpx) scale(0.8) rotateZ(10deg);
  }
}

.title {
  font-size: clamp(48rpx, 10vw, 64rpx);
  font-weight: bold;
  margin-bottom: 30rpx;
  margin-top: 20rpx;
  letter-spacing: 4rpx;
  position: relative;
  width: 100%;
  text-align: center;
  line-height: 1.2;
  z-index: 100;
  padding: 20rpx 0;
  background: linear-gradient(45deg, 
    #00FFFF 0%, 
    #9D4EDD 25%, 
    #FF1493 50%, 
    #9D4EDD 75%, 
    #00FFFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: title-quantum-glow 3s ease-in-out infinite;
}

@keyframes title-quantum-glow {
  0%, 100% {
    filter: brightness(1) saturate(1);
    text-shadow: 0 0 30rpx rgba(0, 255, 255, 0.6);
  }
  50% {
    filter: brightness(1.3) saturate(1.5);
    text-shadow: 0 0 50rpx rgba(157, 78, 221, 0.8);
  }
}

.subtitle {
  font-size: clamp(20rpx, 4vw, 28rpx);
  color: rgba(232, 244, 253, 0.9);
  margin-bottom: clamp(60rpx, 12vw, 80rpx);
  text-align: center;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-shadow: 0 0 15rpx rgba(0, 255, 255, 0.4);
  padding: 0 20rpx;
  word-break: break-word;
}

.subtitle-show {
  animation: subtitle-quantum-appear 0.5s ease-out forwards;
}

@keyframes subtitle-quantum-appear {
  0% {
    opacity: 0;
    transform: translateY(10rpx) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 卡片量子光效 */
.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, 
    rgba(0, 255, 255, 0.15) 0%, 
    rgba(157, 78, 221, 0.1) 30%,
    rgba(255, 20, 147, 0.05) 60%,
    transparent 100%);
  animation: quantum-glow-rotate 12s linear infinite;
  pointer-events: none;
}

@keyframes quantum-glow-rotate {
  0% { 
    transform: rotate(0deg) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 1;
  }
  100% { 
    transform: rotate(360deg) scale(1);
    opacity: 0.6;
  }
}

.input-container {
  width: 100%;
  margin-bottom: 40rpx;
  animation: slide-up 0.8s cubic-bezier(0.23, 1, 0.32, 1) 0.3s forwards;
  opacity: 0;
  transform: translateY(30rpx);
}

/* 量子选择器样式 */
.picker-view {
  background: linear-gradient(135deg, 
    rgba(0, 20, 40, 0.8) 0%, 
    rgba(26, 0, 51, 0.9) 50%,
    rgba(0, 20, 40, 0.8) 100%);
  border: 2rpx solid rgba(0, 255, 255, 0.4);
  border-radius: 20rpx;
  padding: clamp(25rpx, 5vw, 35rpx) clamp(30rpx, 6vw, 40rpx);
  min-height: clamp(80rpx, 16vw, 100rpx);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(15rpx);
  box-shadow: 
    0 8rpx 32rpx rgba(0, 255, 255, 0.2),
    inset 0 2rpx 8rpx rgba(0, 255, 255, 0.1);
}

.picker-view:active {
  transform: scale(0.98);
  border-color: rgba(0, 255, 255, 0.8);
  box-shadow: 
    0 0 40rpx rgba(0, 255, 255, 0.4),
    inset 0 0 20rpx rgba(0, 255, 255, 0.2);
}

.picker-view .placeholder {
  font-size: clamp(26rpx, 5vw, 32rpx);
  color: rgba(232, 244, 253, 0.8);
  flex: 1;
  text-shadow: 0 0 10rpx rgba(0, 255, 255, 0.3);
}

.picker-view .date-icon {
  font-size: clamp(30rpx, 6vw, 36rpx);
  margin-left: 20rpx;
  opacity: 0.7;
  filter: drop-shadow(0 0 8rpx rgba(0, 255, 255, 0.6));
  animation: icon-quantum-float 3s ease-in-out infinite alternate;
}

@keyframes icon-quantum-float {
  0% { 
    transform: translateY(0) rotate(0deg); 
    filter: drop-shadow(0 0 8rpx rgba(0, 255, 255, 0.6));
  }
  100% { 
    transform: translateY(-8rpx) rotate(5deg); 
    filter: drop-shadow(0 0 15rpx rgba(157, 78, 221, 0.8));
  }
}

/* 量子生命统计显示 */
.life-stats {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: clamp(30rpx, 6vw, 40rpx);
  padding: clamp(20rpx, 4vw, 30rpx);
  background: linear-gradient(135deg, 
    rgba(0, 255, 255, 0.08) 0%, 
    rgba(157, 78, 221, 0.06) 50%,
    rgba(255, 20, 147, 0.04) 100%);
  border-radius: 20rpx;
  border: 2rpx solid rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(15rpx);
  box-shadow: 
    0 8rpx 32rpx rgba(0, 255, 255, 0.15),
    inset 0 2rpx 8rpx rgba(0, 255, 255, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  animation: stat-quantum-pulse 4s ease-in-out infinite;
}

@keyframes stat-quantum-pulse {
  0%, 100% { 
    transform: scale(1); 
    filter: brightness(1);
  }
  50% { 
    transform: scale(1.05); 
    filter: brightness(1.2);
  }
}

.stat-label {
  font-size: clamp(22rpx, 4vw, 26rpx);
  color: rgba(232, 244, 253, 0.7);
  margin-bottom: 10rpx;
  text-shadow: 0 0 8rpx rgba(0, 255, 255, 0.3);
}

.stat-value {
  font-size: clamp(32rpx, 6vw, 40rpx);
  font-weight: bold;
  color: #00FFFF;
  margin-bottom: 5rpx;
  text-shadow: 
    0 0 10rpx rgba(0, 255, 255, 0.8),
    0 0 20rpx rgba(0, 255, 255, 0.4);
  animation: value-glow 2s ease-in-out infinite alternate;
}

@keyframes value-glow {
  0% {
    color: #00FFFF;
    text-shadow: 
      0 0 10rpx rgba(0, 255, 255, 0.8),
      0 0 20rpx rgba(0, 255, 255, 0.4);
  }
  100% {
    color: #9D4EDD;
    text-shadow: 
      0 0 15rpx rgba(157, 78, 221, 1),
      0 0 30rpx rgba(157, 78, 221, 0.6);
  }
}

.stat-unit {
  font-size: clamp(20rpx, 4vw, 24rpx);
  color: rgba(232, 244, 253, 0.6);
  text-shadow: 0 0 5rpx rgba(0, 255, 255, 0.2);
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background: linear-gradient(to bottom, 
    transparent, 
    rgba(0, 255, 255, 0.6), 
    rgba(157, 78, 221, 0.4),
    transparent);
  margin: 0 20rpx;
  box-shadow: 0 0 10rpx rgba(0, 255, 255, 0.4);
}

/* 量子按钮优化 */
.calculate-btn {
  width: 100%;
  height: clamp(80rpx, 16vw, 100rpx);
  font-size: clamp(28rpx, 5vw, 34rpx);
  font-weight: bold;
  border-radius: clamp(40rpx, 8vw, 50rpx);
  background: linear-gradient(135deg, 
    #00FFFF 0%, 
    #9D4EDD 25%, 
    #FF1493 50%, 
    #9D4EDD 75%, 
    #00FFFF 100%);
  color: #000000;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 8rpx 30rpx rgba(0, 255, 255, 0.4),
    inset 0 2rpx 10rpx rgba(255, 255, 255, 0.2);
  text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.5);
}

.calculate-btn::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, 
    rgba(255, 255, 255, 0.3) 0%, 
    transparent 70%);
  animation: btn-quantum-pulse 2s ease-in-out infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.calculate-btn:active::before {
  opacity: 1;
}

@keyframes btn-quantum-pulse {
  0%, 100% { 
    transform: scale(0.8) rotate(0deg); 
    opacity: 0;
  }
  50% { 
    transform: scale(1.2) rotate(180deg); 
    opacity: 0.7;
  }
}

.calculate-btn:disabled {
  background: linear-gradient(135deg, 
    rgba(87, 127, 255, 0.3), 
    rgba(160, 160, 160, 0.3));
  color: rgba(232, 244, 253, 0.5);
  box-shadow: none;
  text-shadow: none;
}

.calculate-btn:active {
  transform: scale(0.95);
  box-shadow: 
    0 4rpx 20rpx rgba(0, 255, 255, 0.6),
    inset 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
}

/* 量子加载覆盖层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: 
    radial-gradient(ellipse at center, rgba(26, 0, 51, 0.95) 0%, rgba(0, 0, 0, 0.98) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(15rpx);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: clamp(40rpx, 8vw, 60rpx);
}

.healing-image {
  margin-bottom: clamp(30rpx, 6vw, 40rpx);
}

.simple-drawing {
  font-size: clamp(80rpx, 16vw, 120rpx);
  line-height: 1;
  background: linear-gradient(45deg, 
    #00FFFF 0%, 
    #9D4EDD 50%, 
    #FF1493 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: drawing-quantum-glow 2s ease-in-out infinite alternate;
}

@keyframes drawing-quantum-glow {
  0% { 
    filter: brightness(1) saturate(1);
    transform: scale(1);
  }
  100% { 
    filter: brightness(1.5) saturate(1.5);
    transform: scale(1.1);
  }
}

.loading-text {
  font-size: clamp(24rpx, 5vw, 30rpx);
  color: rgba(232, 244, 253, 0.9);
  text-align: center;
  line-height: 1.5;
  text-shadow: 0 0 15rpx rgba(0, 255, 255, 0.4);
  animation: text-quantum-pulse 1.5s ease-in-out infinite;
}

@keyframes text-quantum-pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* 底部量子引用文字 */
.footer-text {
  position: absolute;
  bottom: calc(env(safe-area-inset-bottom) + 60rpx);
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  text-align: center;
  z-index: 50;
}

.quote {
  font-size: clamp(22rpx, 4vw, 26rpx);
  color: rgba(232, 244, 253, 0.6);
  font-style: italic;
  line-height: 1.5;
  display: block;
  padding: 0 20rpx;
  word-break: break-word;
  text-shadow: 0 0 10rpx rgba(0, 255, 255, 0.3);
  animation: quote-quantum-glow 4s ease-in-out infinite alternate;
}

@keyframes quote-quantum-glow {
  0% { 
    text-shadow: 0 0 10rpx rgba(0, 255, 255, 0.3);
    color: rgba(232, 244, 253, 0.6);
  }
  100% { 
    text-shadow: 0 0 20rpx rgba(157, 78, 221, 0.6);
    color: rgba(232, 244, 253, 0.8);
  }
}

/* 响应式适配 - 超小屏幕 */
@media screen and (max-width: 320px) {
  .container {
    padding: calc(env(safe-area-inset-top) + 40rpx) 3vw calc(env(safe-area-inset-bottom) + 100rpx) 3vw;
  }
  
  .title {
    font-size: 42rpx !important;
    letter-spacing: 2rpx !important;
    margin-bottom: 25rpx !important;
  }
  
  .subtitle {
    font-size: 18rpx !important;
    margin-bottom: 50rpx !important;
  }
  
  .life-stats {
    flex-direction: column;
    gap: 20rpx;
  }
  
  .stat-divider {
    width: 60rpx;
    height: 1px;
    margin: 0;
  }
  
  .footer-text {
    bottom: calc(env(safe-area-inset-bottom) + 40rpx);
  }
}

/* 响应式适配 - 小屏幕 */
@media screen and (max-width: 375px) {
  .container {
    padding: calc(env(safe-area-inset-top) + 45rpx) 4vw calc(env(safe-area-inset-bottom) + 110rpx) 4vw;
  }
  
  .title {
    font-size: clamp(44rpx, 9vw, 56rpx) !important;
    letter-spacing: 3rpx !important;
  }
  
  .life-stats {
    padding: 25rpx 15rpx;
  }
}

/* 响应式适配 - 大屏幕 */
@media screen and (min-width: 414px) {
  .container {
    max-width: 750rpx;
    margin: 0 auto;
    padding: calc(env(safe-area-inset-top) + 70rpx) 6vw calc(env(safe-area-inset-bottom) + 140rpx) 6vw;
  }
  
  .life-stats {
    max-width: 600rpx;
    margin: 0 auto clamp(30rpx, 6vw, 40rpx) auto;
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
  .container {
    padding: calc(env(safe-area-inset-top) + 30rpx) 6vw calc(env(safe-area-inset-bottom) + 80rpx) 6vw;
  }
  
  .title {
    font-size: clamp(40rpx, 8vw, 52rpx) !important;
    margin: 15rpx 0 25rpx 0 !important;
  }
  
  .subtitle {
    margin-bottom: 40rpx !important;
  }
  
  .footer-text {
    bottom: calc(env(safe-area-inset-bottom) + 30rpx);
  }
  
  .quote {
    font-size: clamp(20rpx, 3.5vw, 24rpx) !important;
  }
}

.glow-follower {
  z-index: -2;
}

.futuristic-bg {
  z-index: -2;
}

/* 页面容器 */
.page-container {
  position: relative;
  z-index: 1;
}

/* 标题区域 */
.title-section {
  margin-bottom: var(--spacing-2xl);
  text-align: center;
  animation: card-enter 0.6s ease-out forwards;
}

.title {
  font-size: clamp(48rpx, 8vw, 80rpx);
  font-weight: 600;
  background: linear-gradient(135deg, var(--accent-500) 0%, var(--primary-500) 70%, var(--accent-400) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-md);
  letter-spacing: 2rpx;
  text-shadow: 0 0 20rpx rgba(23, 224, 255, 0.3);
}

.subtitle {
  font-size: clamp(24rpx, 4vw, 32rpx);
  color: var(--text-secondary);
  font-weight: 400;
  line-height: 1.5;
  opacity: 0.9;
}

/* 生日输入卡片 */
.birth-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  backdrop-filter: blur(var(--blur));
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  width: 100%;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: var(--shadow-sm);
  animation: card-enter 0.8s ease-out 0.2s both;
}

.birth-card:hover {
  background: var(--glass-hover);
  border-color: rgba(255, 255, 255, 0.12);
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.card-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.card-label::before {
  content: '';
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: var(--accent-500);
  box-shadow: 0 0 8rpx var(--accent-500);
}

/* 选择器样式 */
.picker-view {
  height: 200rpx;
  border-radius: var(--border-radius-input);
  background: var(--glass-hover);
  border: 1px solid var(--glass-border);
  overflow: hidden;
  position: relative;
}

.picker-view::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 60rpx;
  transform: translateY(-50%);
  background: rgba(23, 224, 255, 0.05);
  border: 1px solid rgba(23, 224, 255, 0.2);
  border-left: none;
  border-right: none;
  z-index: 1;
  pointer-events: none;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  font-size: 32rpx;
  color: var(--text-tertiary);
  transition: all 0.3s ease;
  position: relative;
}

.picker-item.active {
  color: var(--text-primary);
  font-weight: 600;
  text-shadow: 0 0 8rpx rgba(23, 224, 255, 0.5);
}

/* 预期寿命输入区域 */
.life-input-container {
  margin-bottom: var(--spacing-md);
}

.life-input {
  background: var(--glass-hover);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-input);
  padding: var(--spacing-md);
  color: var(--text-primary);
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(var(--blur));
  box-sizing: border-box;
  width: 100%;
  background-image: linear-gradient(90deg, transparent 0%, rgba(23, 224, 255, 0.02) 50%, transparent 100%);
}

.life-input:focus {
  border-color: var(--accent-500);
  background: rgba(23, 224, 255, 0.08);
  outline: none;
  box-shadow: 0 0 0 2rpx rgba(23, 224, 255, 0.2);
  text-shadow: 0 0 4rpx rgba(23, 224, 255, 0.3);
}

.life-input::placeholder {
  color: var(--text-tertiary);
  font-weight: 400;
}

/* 生活状态显示区域 */
.life-stats {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  backdrop-filter: blur(var(--blur));
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  position: relative;
  overflow: hidden;
  animation: card-enter 1.0s ease-out 0.4s both;
}

.life-stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, var(--accent-500) 50%, transparent 100%);
  opacity: 0.6;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--glass-hover);
  border-radius: var(--border-radius-input);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item:hover {
  transform: translateY(-2rpx);
  background: rgba(23, 224, 255, 0.08);
  border-color: rgba(23, 224, 255, 0.2);
}

.stat-value {
  font-size: clamp(32rpx, 6vw, 48rpx);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  text-shadow: 0 0 8rpx rgba(23, 224, 255, 0.3);
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 400;
}

/* 按钮样式 */
.calculate-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--accent-500) 0%, var(--primary-500) 100%);
  border: none;
  border-radius: var(--border-radius-btn);
  color: var(--text-primary);
  font-weight: 600;
  font-size: 32rpx;
  line-height: 88rpx;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-lg);
  animation: card-enter 1.2s ease-out 0.6s both;
}

.calculate-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.calculate-btn:hover::before {
  left: 100%;
}

.calculate-btn:active {
  transform: scale(0.97);
  opacity: 0.9;
  box-shadow: var(--shadow-sm);
}

.calculate-btn:disabled {
  background: var(--surface-700);
  color: var(--text-muted);
  cursor: not-allowed;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(11, 11, 17, 0.8);
  backdrop-filter: blur(var(--blur));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  animation: fade-in 0.3s ease forwards;
}

@keyframes fade-in {
  to {
    opacity: 1;
  }
}

.loading-content {
  text-align: center;
  color: var(--text-primary);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--surface-700);
  border-top: 4rpx solid var(--accent-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 响应式优化 */
@media screen and (max-width: 320px) {
  .birth-card,
  .life-stats {
    padding: var(--spacing-lg);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .picker-view {
    height: 160rpx;
  }
}

@media screen and (max-width: 375px) {
  .title-section {
    margin-bottom: var(--spacing-xl);
  }
  
  .stats-grid {
    gap: var(--spacing-md);
  }
}

@media screen and (min-width: 414px) {
  .birth-card,
  .life-stats {
    padding: 60rpx;
  }
  
  .stats-grid {
    gap: var(--spacing-xl);
  }
}

@media screen and (min-width: 500px) {
  .title-section {
    margin-bottom: 80rpx;
  }
  
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .picker-view {
    height: 240rpx;
  }
}

/* 横屏模式 */
@media screen and (orientation: landscape) {
  .title-section {
    margin-bottom: var(--spacing-lg);
  }
  
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
  }
  
  .birth-card,
  .life-stats {
    margin-bottom: var(--spacing-md);
  }
} 