/* pages/index/index.wxss - 简洁版 */

/* 容器 */
.container {
  padding: calc(env(safe-area-inset-top) + 40rpx) clamp(32rpx, 6vw, 48rpx) calc(env(safe-area-inset-bottom) + 120rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  max-width: 750rpx;
  margin: 0 auto;
  position: relative;
}

/* 标题 - 简洁版 */
.title {
  font-size: clamp(48rpx, 10vw, 64rpx);
  font-weight: 600;
  margin: 40rpx 0 16rpx;
  letter-spacing: 1rpx;
  text-align: center;
  line-height: 1.3;
  color: var(--text-primary);
}

/* 副标题 - 简洁版 */
.subtitle {
  font-size: clamp(24rpx, 5vw, 28rpx);
  color: var(--text-secondary);
  margin-bottom: 32rpx;
  text-align: center;
  font-weight: 400;
  line-height: 1.5;
  padding: 0 32rpx;
}

/* 卡片 - 简洁版 */
.futuristic-card {
  width: 100%;
  max-width: 600rpx;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.futuristic-card:hover {
  background: var(--card-hover);
  box-shadow: var(--shadow-md);
}

/* 输入容器 */
.input-container {
  width: 100%;
  margin-bottom: var(--spacing-lg);
}

/* 选择器样式 - 简洁版 */
.picker-view {
  width: 100%;
  background: var(--surface-primary);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius-input);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all var(--transition-fast);
}

.picker-view:active {
  background: var(--card-hover);
  border-color: var(--primary-400);
  transform: scale(0.98);
}

.placeholder {
  color: var(--text-primary);
  font-size: 28rpx;
  font-weight: 400;
}

.date-icon {
  font-size: 32rpx;
  color: var(--primary-400);
}

/* 生命统计显示 - 简洁版 */
.life-stats {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--surface-primary);
  border-radius: var(--border-radius);
  border: 1px solid var(--card-border);
  box-shadow: var(--shadow-sm);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-label {
  font-size: 22rpx;
  color: var(--text-tertiary);
  margin-bottom: 8rpx;
  font-weight: 400;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.stat-unit {
  font-size: 20rpx;
  color: var(--text-muted);
}

.stat-divider {
  width: 1px;
  height: 40rpx;
  background: var(--card-border);
  margin: 0 var(--spacing-md);
}

/* 按钮 - 简洁版 */
.calculate-btn {
  width: 100%;
  height: 88rpx;
  background: var(--primary-500);
  border: none;
  border-radius: var(--border-radius-btn);
  color: var(--text-primary);
  font-weight: 500;
  font-size: 32rpx;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
  cursor: pointer;
}

.calculate-btn:hover {
  background: var(--primary-400);
  box-shadow: var(--shadow-md);
}

.calculate-btn:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-sm);
}

.calculate-btn:disabled {
  background: var(--surface-secondary);
  color: var(--text-muted);
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.6;
}

/* 底部文字 - 简洁版 */
.footer-text {
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom) + 60rpx);
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  width: 90%;
  z-index: 10;
}

.quote {
  font-size: 24rpx;
  color: var(--text-tertiary);
  line-height: 1.5;
  font-style: italic;
}

/* 设备特定样式 */
.device-small .title {
  font-size: clamp(38rpx, 8vw, 46rpx) !important;
  letter-spacing: 1rpx !important;
  margin-bottom: clamp(20rpx, 4vw, 28rpx) !important;
}

.device-small .subtitle {
  font-size: clamp(16rpx, 3.5vw, 20rpx) !important;
  margin-bottom: clamp(40rpx, 8vw, 60rpx) !important;
}

.device-small .futuristic-card {
  padding: clamp(20rpx, 4vw, 32rpx) !important;
  margin-bottom: clamp(16rpx, 3vw, 24rpx) !important;
}

.device-small .life-stats {
  flex-direction: column;
  gap: clamp(16rpx, 3vw, 24rpx);
  padding: clamp(20rpx, 4vw, 28rpx);
}

.device-small .stat-divider {
  width: 60rpx;
  height: 1px;
  margin: 0;
}

.device-small .calculate-btn {
  font-size: clamp(26rpx, 5vw, 32rpx) !important;
  line-height: clamp(70rpx, 14vw, 88rpx) !important;
}

/* 中等设备样式 */
.device-medium .title {
  font-size: clamp(48rpx, 9vw, 60rpx) !important;
  letter-spacing: 2rpx !important;
}

.device-medium .subtitle {
  font-size: clamp(20rpx, 4vw, 26rpx) !important;
}

.device-medium .futuristic-card {
  padding: clamp(28rpx, 5vw, 40rpx) !important;
}

/* 大屏设备样式 */
.device-large .title {
  font-size: clamp(52rpx, 10vw, 68rpx) !important;
  letter-spacing: 3rpx !important;
}

.device-large .subtitle {
  font-size: clamp(22rpx, 4.5vw, 28rpx) !important;
}

.device-large .futuristic-card {
  padding: clamp(32rpx, 6vw, 48rpx) !important;
  max-width: 650rpx !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* 平板设备样式 */
.device-tablet .container {
  max-width: 800rpx !important;
  margin: 0 auto !important;
}

.device-tablet .title {
  font-size: clamp(60rpx, 12vw, 80rpx) !important;
  letter-spacing: 4rpx !important;
}

.device-tablet .subtitle {
  font-size: clamp(24rpx, 5vw, 32rpx) !important;
}

.device-tablet .futuristic-card {
  padding: clamp(40rpx, 7vw, 60rpx) !important;
  max-width: 700rpx !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* 响应式适配 */
@media screen and (max-width: 320px) {
  .container {
    padding: calc(env(safe-area-inset-top) + 40rpx) clamp(16rpx, 4vw, 24rpx) calc(env(safe-area-inset-bottom) + 100rpx);
  }
  
  .footer-text {
    bottom: calc(env(safe-area-inset-bottom) + 40rpx);
    padding: 0 clamp(16rpx, 4vw, 24rpx);
  }
  
  .quote {
    font-size: clamp(18rpx, 3.5vw, 22rpx) !important;
    line-height: 1.4 !important;
  }
}

@media screen and (max-width: 375px) {
  .container {
    padding: calc(env(safe-area-inset-top) + 45rpx) clamp(20rpx, 4vw, 32rpx) calc(env(safe-area-inset-bottom) + 110rpx);
  }
  
  .life-stats {
    padding: clamp(20rpx, 4vw, 32rpx) clamp(12rpx, 2.5vw, 20rpx);
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
  .container {
    padding: calc(env(safe-area-inset-top) + 20rpx) clamp(32rpx, 8vw, 64rpx) calc(env(safe-area-inset-bottom) + 60rpx);
    max-width: 90vw;
    margin: 0 auto;
  }
  
  .futuristic-card {
    max-width: none;
    margin-bottom: clamp(16rpx, 3vw, 24rpx);
    padding: clamp(24rpx, 4vw, 40rpx);
  }
  
  .title {
    font-size: clamp(36rpx, 6vw, 48rpx);
    margin-bottom: clamp(16rpx, 3vw, 24rpx);
  }
  
  .subtitle {
    font-size: clamp(20rpx, 4vw, 24rpx);
    margin-bottom: clamp(24rpx, 5vw, 32rpx);
  }
  
  .calculate-btn {
    line-height: clamp(60rpx, 12vw, 80rpx);
    font-size: clamp(28rpx, 5vw, 36rpx);
  }
}
