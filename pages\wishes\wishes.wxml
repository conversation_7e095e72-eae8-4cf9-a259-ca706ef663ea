<!--pages/wishes/wishes.wxml-->
<view class="container">
  <view class="futuristic-bg"></view>
  
  <view class="title neon-text">愿望清单</view>
  
  <view class="input-section futuristic-card">
    <view class="input-row">
    <input 
      class="wish-input" 
      placeholder="输入您想要完成的事情..." 
      value="{{newWish}}"
      bindinput="onInputChange"
      confirm-type="done"
    />
    </view>
    
    <!-- 添加目标日期选择 -->
    <view class="date-row">
      <text class="date-label">目标完成日期：</text>
      <picker mode="date" 
              value="{{newWishDate}}" 
              start="{{today}}" 
              end="{{maxDate}}" 
              bindchange="onWishDateChange">
        <view class="wish-date-picker {{newWishDate ? 'has-date' : ''}}">
          <text class="wish-date-text">{{newWishDate || '选择日期（可选）'}}</text>
          <text class="date-icon">📅</text>
        </view>
      </picker>
    </view>
    
    <view class="btn-row">
      <button class="add-btn" bindtap="addWish">添加愿望</button>
      <button class="clear-date-btn" wx:if="{{newWishDate}}" bindtap="clearDate">清除日期</button>
    </view>
  </view>
  
  <view class="wishes-list">
    <block wx:if="{{wishes.length > 0}}">
      <view class="list-header">
        <text>长按项目可以删除 • 点击可查看详情</text>
      </view>
      
      <!-- 按类别分组显示 -->
      <view class="category-section" wx:if="{{urgentWishes.length > 0}}">
        <view class="category-title urgent">🔥 紧急愿望（30天内）</view>
        <view 
          class="wish-item futuristic-card urgent-wish" 
          wx:for="{{urgentWishes}}" 
          wx:key="id" 
          bindlongpress="deleteWish" 
          bindtap="toggleWishDetail"
          data-id="{{item.id}}"
        >
          <view class="wish-content">{{item.content}}</view>
          <view class="wish-meta">
            <text class="wish-time">创建：{{item.createTime ? item.createTime.substring(0, 10) : ''}}</text>
            <text class="wish-target-date" wx:if="{{item.targetDate}}">
              目标：{{item.targetDate}}
              <text class="days-left urgent-days">({{item.daysLeft}}天后)</text>
            </text>
          </view>
          <view class="wish-detail" wx:if="{{item.showDetail}}">
            <text class="detail-text">{{item.detailInfo}}</text>
          </view>
        </view>
      </view>
      
      <view class="category-section" wx:if="{{shortTermWishes.length > 0}}">
        <view class="category-title short-term">⏰ 短期愿望（1年内）</view>
        <view 
          class="wish-item futuristic-card short-term-wish" 
          wx:for="{{shortTermWishes}}" 
          wx:key="id" 
          bindlongpress="deleteWish" 
          bindtap="toggleWishDetail"
          data-id="{{item.id}}"
        >
          <view class="wish-content">{{item.content}}</view>
          <view class="wish-meta">
            <text class="wish-time">创建：{{item.createTime ? item.createTime.substring(0, 10) : ''}}</text>
            <text class="wish-target-date" wx:if="{{item.targetDate}}">
              目标：{{item.targetDate}}
              <text class="days-left short-days">({{item.daysLeft}}天后)</text>
            </text>
          </view>
          <view class="wish-detail" wx:if="{{item.showDetail}}">
            <text class="detail-text">{{item.detailInfo}}</text>
          </view>
        </view>
      </view>
      
      <view class="category-section" wx:if="{{longTermWishes.length > 0}}">
        <view class="category-title long-term">🌟 长期愿望（1年以上）</view>
        <view 
          class="wish-item futuristic-card long-term-wish" 
          wx:for="{{longTermWishes}}" 
          wx:key="id" 
          bindlongpress="deleteWish" 
          bindtap="toggleWishDetail"
          data-id="{{item.id}}"
        >
          <view class="wish-content">{{item.content}}</view>
          <view class="wish-meta">
            <text class="wish-time">创建：{{item.createTime ? item.createTime.substring(0, 10) : ''}}</text>
            <text class="wish-target-date" wx:if="{{item.targetDate}}">
              目标：{{item.targetDate}}
              <text class="days-left long-days">({{item.daysLeft}}天后)</text>
            </text>
          </view>
          <view class="wish-detail" wx:if="{{item.showDetail}}">
            <text class="detail-text">{{item.detailInfo}}</text>
          </view>
        </view>
      </view>
      
      <view class="category-section" wx:if="{{noDateWishes.length > 0}}">
        <view class="category-title no-date">💭 随想愿望（无时限）</view>
      <view 
          class="wish-item futuristic-card no-date-wish" 
          wx:for="{{noDateWishes}}" 
        wx:key="id" 
        bindlongpress="deleteWish" 
          bindtap="toggleWishDetail"
        data-id="{{item.id}}"
      >
        <view class="wish-content">{{item.content}}</view>
          <view class="wish-meta">
            <text class="wish-time">创建：{{item.createTime ? item.createTime.substring(0, 10) : ''}}</text>
          </view>
          <view class="wish-detail" wx:if="{{item.showDetail}}">
            <text class="detail-text">{{item.detailInfo}}</text>
          </view>
        </view>
      </view>
    </block>
    
    <view wx:else class="empty-list">
      <text>暂无愿望，添加一些想在有生之年完成的事吧！</text>
    </view>
  </view>
</view> 