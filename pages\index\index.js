// 引入动画工具
const animationUtil = require('../../utils/animation.js');

Page({
  data: {
    birthDate: '',
    targetDays: 30000,
    particles: [],
    stars: [],
    digitalRain: [],
    isAnimating: false,
    cardStyle: '', // 用于3D卡片倾斜效果
    glowPosition: {
      x: 0,
      y: 0,
      opacity: 0
    },
    // 动态文本相关
    currentSubtitle: '',
    subtitleVisible: false,
    currentQuote: '',
    buttonText: '开始时间旅行',
    // 生命统计
    livedDays: 0,
    remainingDays: 0,
    // 动画状态    floatingLabels: [],    showLoading: false,    loadingText: '正在计算生命轨迹...',    showRipple: false,    ripplePosition: { x: 0, y: 0 },    currentHealingImage: '🌟'
  },

  // 预设的字幕和名言
  subtitles: [
    '输入您的出生日期，探索生命旅程',
    '时间如流水，珍惜每一刻',
    '生命不在于长短，而在于宽度',
    '每一天都是全新的开始'
  ],

  quotes: [
    '时间是构成生命的材料',
    '生命不在于呼吸的次数，而在于那些令你无法呼吸的时刻',
    '不要等待，时机永远不会恰到好处',
    '昨天是历史，明天是谜题，今天是礼物',
    '时间最公平，给每个人都是24小时'
  ],

  onLoad: function (options) {
    // 页面加载时执行
    const targetDays = wx.getStorageSync('targetDays') || 30000;
    this.setData({
      targetDays: targetDays
    });
    
    // 初始化各种效果
    this.initAllEffects();
    
    // 添加页面进入动画
    animationUtil.applyPageEnterAnimation(this);
  },

  onShow: function () {
    // 页面显示时执行
    const targetDays = wx.getStorageSync('targetDays') || 30000;
    this.setData({
      targetDays: targetDays
    });
    
    // 更新生命统计
    this.updateLifeStats();
    
    // 更新tabBar状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateSelected();
    }
  },

  // 初始化所有效果
  initAllEffects: function() {
    this.createParticles();
    this.createStarField();
    this.createDigitalRain();
    this.startDynamicText();
    this.rotateQuotes();
  },

  // 动态字幕效果
  startDynamicText: function() {
    let subtitleIndex = 0;
    
    const showNextSubtitle = () => {
      const subtitle = this.subtitles[subtitleIndex];
      this.setData({
        currentSubtitle: '',
        subtitleVisible: false
      });
      
      // 打字机效果
      setTimeout(() => {
        animationUtil.typewriterEffect(this, subtitle, 'currentSubtitle', 80);
        this.setData({
          subtitleVisible: true
        });
      }, 300);
      
      subtitleIndex = (subtitleIndex + 1) % this.subtitles.length;
    };
    
    showNextSubtitle();
    setInterval(showNextSubtitle, 4000);
  },

  // 轮播名言
  rotateQuotes: function() {
    let quoteIndex = 0;
    
    const showNextQuote = () => {
      this.setData({
        currentQuote: this.quotes[quoteIndex]
      });
      quoteIndex = (quoteIndex + 1) % this.quotes.length;
    };
    
    showNextQuote();
    setInterval(showNextQuote, 6000);
  },

  // 创建星空背景
  createStarField: function() {
    animationUtil.createStarField(this, 30);
  },

  // 创建数字雨效果
  createDigitalRain: function() {
    const rain = [];
    const chars = ['0', '1', '⚡', '✦', '◆', '●', '▲'];
    
    for (let i = 0; i < 8; i++) {
      const column = {
        left: Math.random() * 100,
        delay: Math.random() * 5,
        duration: Math.random() * 3 + 8,
        chars: Array.from({length: 15}, () => chars[Math.floor(Math.random() * chars.length)])
      };
      rain.push(column);
    }
    
    this.setData({
      digitalRain: rain
    });
  },

  // 更新生命统计
  updateLifeStats: function() {
    if (!this.data.birthDate) return;
    
    const birth = new Date(this.data.birthDate);
    const now = new Date();
    const lived = Math.floor((now - birth) / (1000 * 60 * 60 * 24));
    const remaining = Math.max(0, this.data.targetDays - lived);
    
    // 使用数字动画
    animationUtil.animateNumber(this, lived, 'livedDays', 1500);
    animationUtil.animateNumber(this, remaining, 'remainingDays', 1500);
  },
  
  // 选择生日
  onBirthDateChange: function(e) {
    this.setData({
      birthDate: e.detail.value
    });
    
    // 保存生日到缓存
    wx.setStorageSync('birthDate', e.detail.value);
    
    // 更新生命统计
    this.updateLifeStats();
    
    // 创建浮动标签
    animationUtil.createFloatingLabel(this, '生日已设置 ✨');
    
    // 震动反馈
    animationUtil.vibrate('light');
  },
  
  // 开始倒计时，跳转到结果页
  startCountdown: function(e) {
    if (!this.data.birthDate) {
      wx.showToast({
        title: '请先选择生日',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.isAnimating) {
      return;
    }
    
    // 创建波纹效果
    animationUtil.createRippleEffect(e, this);
    
    // 震动反馈
    animationUtil.vibrate('medium');
    
    // 设置正在动画状态
    this.setData({
      isAnimating: true,
      buttonText: '计算中...'
    });
    
    // 显示加载动画
    animationUtil.createLoadingAnimation(this, true);
    
    // 创建鼓励标签
    animationUtil.createFloatingLabel(this, '开始探索人生 🚀');
    
    setTimeout(() => {
      // 应用页面离开动画，然后跳转
      animationUtil.applyPageExitAnimation(this, () => {
        wx.navigateTo({
          url: `/pages/result/result?birth=${this.data.birthDate}&targetDays=${this.data.targetDays}`,
          success: () => {
            // 重置状态
            this.setData({
              isAnimating: false,
              buttonText: '开始时间旅行'
            });
          }
        });
      });
    }, 2000);
  },
  
  // 创建粒子效果
  createParticles: function() {
    const particles = [];
    const count = 20; // 增加粒子数量
    
    for (let i = 0; i < count; i++) {
      particles.push({
        left: Math.random() * 100 + '%',
        top: Math.random() * 100 + '%',
        size: Math.random() * 12 + 4 + 'rpx',
        animationDelay: Math.random() * 15 + 's',
        animationDuration: Math.random() * 15 + 20 + 's'
      });
    }
    
    this.setData({
      particles: particles
    });
  },
  
  // 卡片触摸开始 - 激活3D倾斜效果
  onCardTouchStart: function(e) {
    animationUtil.createCardTiltEffect(e, this, '.card-3d');
    animationUtil.vibrate('light');
  },
  
  // 卡片触摸移动 - 更新3D倾斜效果
  onCardTouchMove: function(e) {
    animationUtil.createCardTiltEffect(e, this, '.card-3d');
  },
  
  // 卡片触摸结束 - 重置倾斜效果
  onCardTouchEnd: function() {
    animationUtil.resetCardTiltEffect(this, '.card-3d');
  },
  
  // 页面触摸 - 创建跟随光效
  onPageTouch: function(e) {
    animationUtil.createFollowLight(e, this);
  }
}) 