// 引入设备适配工具
const deviceUtils = require('../../utils/deviceUtils.js');

Page({
  data: {
    birthDate: '',
    targetDays: 30000,
    particles: [],
    stars: [],
    digitalRain: [],
    isAnimating: false,
    cardStyle: '', // 用于3D卡片倾斜效果
    glowPosition: {
      x: 0,
      y: 0,
      opacity: 0
    },
    // 动态文本相关
    currentSubtitle: '',
    subtitleVisible: false,
    currentQuote: '',
    buttonText: '开始时间旅行',
    // 生命统计
    livedDays: 0,
    remainingDays: 0,
    // 动画状态    floatingLabels: [],    showLoading: false,    loadingText: '正在计算生命轨迹...',    showRipple: false,    ripplePosition: { x: 0, y: 0 },    currentHealingImage: '🌟'
  },

  // 预设的字幕和名言
  subtitles: [
    '输入您的出生日期，探索生命旅程',
    '时间如流水，珍惜每一刻',
    '生命不在于长短，而在于宽度',
    '每一天都是全新的开始'
  ],

  quotes: [
    '时间是构成生命的材料',
    '生命不在于呼吸的次数，而在于那些令你无法呼吸的时刻',
    '不要等待，时机永远不会恰到好处',
    '昨天是历史，明天是谜题，今天是礼物',
    '时间最公平，给每个人都是24小时'
  ],

  onLoad: function (options) {
    // 页面加载时执行
    const targetDays = wx.getStorageSync('targetDays') || 30000;

    // 获取设备适配信息
    const app = getApp();
    const deviceClassName = app.globalData.deviceClassName || '';
    const adaptiveParams = app.globalData.adaptiveParams || {};

    this.setData({
      targetDays: targetDays,
      deviceClassName: deviceClassName,
      adaptiveParams: adaptiveParams
    });

    // 初始化页面
    this.initPage();
  },

  onShow: function () {
    // 页面显示时执行
    const targetDays = wx.getStorageSync('targetDays') || 30000;
    this.setData({
      targetDays: targetDays
    });
    
    // 更新生命统计
    this.updateLifeStats();
    
    // 更新tabBar状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateSelected();
    }
  },

  // 初始化页面
  initPage: function() {
    // 简单的页面初始化，不使用复杂动画
    console.log('页面初始化完成');
  },

  // 更新生命统计
  updateLifeStats: function() {
    if (!this.data.birthDate) return;

    const birth = new Date(this.data.birthDate);
    const now = new Date();
    const lived = Math.floor((now - birth) / (1000 * 60 * 60 * 24));
    const remaining = Math.max(0, this.data.targetDays - lived);

    // 直接设置数据，不使用动画
    this.setData({
      livedDays: lived,
      remainingDays: remaining
    });
  },
  
  // 选择生日
  onBirthDateChange: function(e) {
    this.setData({
      birthDate: e.detail.value
    });

    // 保存生日到缓存
    wx.setStorageSync('birthDate', e.detail.value);

    // 更新生命统计
    this.updateLifeStats();

    // 简单的提示
    wx.showToast({
      title: '生日已设置',
      icon: 'success',
      duration: 1500
    });
  },
  
  // 开始倒计时，跳转到结果页
  startCountdown: function(e) {
    if (!this.data.birthDate) {
      wx.showToast({
        title: '请先选择生日',
        icon: 'none'
      });
      return;
    }

    // 直接跳转，不使用复杂动画
    wx.navigateTo({
      url: `/pages/result/result?birth=${this.data.birthDate}&targetDays=${this.data.targetDays}`
    });
  },
  
  // 移除所有动画相关方法
}) 