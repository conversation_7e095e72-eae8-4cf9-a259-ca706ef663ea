// 引入设备适配工具
const deviceUtils = require('./utils/deviceUtils.js');

App({
  onLaunch: function() {
    // 小程序启动时执行的逻辑
    this.initDeviceAdaptation();
  },

  // 初始化设备适配
  initDeviceAdaptation: function() {
    try {
      // 获取系统信息
      const systemInfo = deviceUtils.getSystemInfo();
      const deviceType = deviceUtils.getDeviceType();
      const safeAreaInfo = deviceUtils.getSafeAreaInfo();
      const isNotch = deviceUtils.isNotchScreen();

      // 保存到全局数据
      this.globalData.systemInfo = systemInfo;
      this.globalData.deviceType = deviceType;
      this.globalData.safeAreaInfo = safeAreaInfo;
      this.globalData.isNotchScreen = isNotch;
      this.globalData.deviceClassName = deviceUtils.getDeviceClassName();

      console.log('设备适配信息:', {
        deviceType,
        screenSize: `${systemInfo.windowWidth}x${systemInfo.windowHeight}`,
        pixelRatio: systemInfo.pixelRatio,
        isNotch,
        platform: systemInfo.platform,
        model: systemInfo.model
      });

      // 设置全局CSS变量（如果支持）
      this.setGlobalCSSVariables(systemInfo, safeAreaInfo);

    } catch (error) {
      console.error('设备适配初始化失败:', error);
    }
  },

  // 设置全局CSS变量
  setGlobalCSSVariables: function(systemInfo, safeAreaInfo) {
    try {
      // 计算适配参数
      const deviceType = deviceUtils.getDeviceType();
      const baseFontSize = deviceUtils.getAdaptiveFontSize(32);
      const baseSpacing = deviceUtils.getAdaptiveSpacing(24);

      // 保存适配参数到全局数据，供页面使用
      this.globalData.adaptiveParams = {
        baseFontSize,
        baseSpacing,
        safeAreaTop: safeAreaInfo.top,
        safeAreaBottom: systemInfo.windowHeight - safeAreaInfo.bottom,
        screenWidth: systemInfo.windowWidth,
        screenHeight: systemInfo.windowHeight,
        pixelRatio: systemInfo.pixelRatio
      };

    } catch (error) {
      console.error('设置CSS变量失败:', error);
    }
  },

  globalData: {
    // 设备信息
    systemInfo: null,
    deviceType: null,
    safeAreaInfo: null,
    isNotchScreen: false,
    deviceClassName: '',
    adaptiveParams: null
  }
})