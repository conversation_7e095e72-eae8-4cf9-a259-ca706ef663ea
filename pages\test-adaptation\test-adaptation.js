// pages/test-adaptation/test-adaptation.js
const deviceUtils = require('../../utils/deviceUtils.js');

Page({
  data: {
    systemInfo: {},
    deviceType: '',
    safeAreaInfo: {},
    isNotchScreen: false,
    deviceClassName: '',
    adaptiveParams: {},
    testResults: []
  },

  onLoad: function (options) {
    this.runAdaptationTests();
  },

  // 运行适配测试
  runAdaptationTests: function() {
    const app = getApp();
    const systemInfo = deviceUtils.getSystemInfo();
    const deviceType = deviceUtils.getDeviceType();
    const safeAreaInfo = deviceUtils.getSafeAreaInfo();
    const isNotchScreen = deviceUtils.isNotchScreen();
    const deviceClassName = deviceUtils.getDeviceClassName();
    
    // 测试响应式尺寸
    const testSizes = {
      small: 100,
      medium: 120,
      large: 140,
      tablet: 160
    };
    const responsiveSize = deviceUtils.getResponsiveSize(testSizes);
    
    // 测试字体适配
    const adaptiveFontSize = deviceUtils.getAdaptiveFontSize(32);
    
    // 测试间距适配
    const adaptiveSpacing = deviceUtils.getAdaptiveSpacing(24);
    
    // 测试Canvas适配参数
    const mockCanvasInfo = { width: 300, height: 300 };
    const canvasParams = deviceUtils.getCanvasAdaptParams(mockCanvasInfo);
    
    // 测试单位转换
    const rpxToPxResult = deviceUtils.rpxToPx(100);
    const pxToRpxResult = deviceUtils.pxToRpx(50);
    
    const testResults = [
      { name: '设备类型', value: deviceType },
      { name: '屏幕尺寸', value: `${systemInfo.windowWidth}x${systemInfo.windowHeight}` },
      { name: '像素比', value: systemInfo.pixelRatio },
      { name: '是否刘海屏', value: isNotchScreen ? '是' : '否' },
      { name: '安全区域顶部', value: `${safeAreaInfo.top}px` },
      { name: '安全区域底部', value: `${systemInfo.windowHeight - safeAreaInfo.bottom}px` },
      { name: '响应式尺寸', value: `${responsiveSize}rpx` },
      { name: '适配字体大小', value: `${adaptiveFontSize}rpx` },
      { name: '适配间距', value: `${adaptiveSpacing}rpx` },
      { name: 'Canvas缩放', value: canvasParams.contentScale },
      { name: '100rpx转px', value: `${rpxToPxResult.toFixed(2)}px` },
      { name: '50px转rpx', value: `${pxToRpxResult.toFixed(2)}rpx` }
    ];

    this.setData({
      systemInfo,
      deviceType,
      safeAreaInfo,
      isNotchScreen,
      deviceClassName,
      adaptiveParams: app.globalData.adaptiveParams || {},
      testResults
    });
  },

  // 返回首页
  backToHome: function() {
    wx.navigateBack();
  }
});
